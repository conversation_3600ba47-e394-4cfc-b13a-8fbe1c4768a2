# 线上环境
VITE_USER_NODE_ENV = production

# 公共基础路径
VITE_PUBLIC_PATH = /

# 路由模式
# Optional: hash | history
VITE_ROUTER_MODE = hash

# 是否启用 gzip 或 brotli 压缩打包，如果需要多个压缩规则，可以使用 “,” 分隔
# Optional: gzip | brotli | none
VITE_BUILD_COMPRESS = none

# 打包压缩后是否删除源文件
VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE = false

# 打包时是否删除 console
VITE_DROP_CONSOLE = true

# Websocket IP地址
VITE_CHAT_WEBSOCKET = "************:8088"

# 预览服务地址
VITE_CHAT_PREVIEW = "http://************:8088"

# 线上环境接口地址
# VITE_API_URL = "/"
# 客户端打包运行模式专属
VITE_API_URL = "http://************:8088/"
#文件下载接口地址
VITE_API_URL2 = "http://************:8088/admin-api/infra/file/downloadByUrl"
# 百灵内嵌地址 Pid
VITE_BAILING_URL = "http://************:8088/bailing/newchat?routerId=dialogue"

# 文件解读地址 Pid downloadUrl fileName secretLevel
VITE_FILE_DECODE_URL = "http://************:8088/bailing/dialogWindow"

# 问题反馈地址 Pid
VITE_FEEDBACK_URL = "http://************:8088/feedback"

# 首页智慧屏地址 Pid
VITE_LARKSMART_URL = "http://************:8088/larkSmart/home"

# 工具协同
VITE_TOOL_URL = "http://***********:8085"

# openApi开放平台
VITE_OPENAPI_URL = "http://************:8088/oi"

# 帮助文档
VITE_HELP_URL = "http://************:8088/help.pdf"

# 桌面悬浮窗
VITE_CHAT_URL1 = "http://************:8088/directBailing/chatRpc/streamChatRpc"
VITE_CHAT_URL2 = "http://************:8088/admin-api/chat/llm/stream/openai"