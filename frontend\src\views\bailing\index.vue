<template>
  <iframe class="w-full h-full p-0 m-0 border-none" :src="innerUrl" frameborder="0"></iframe>
</template>

<script setup lang="ts" name="bailing">
import { computed } from "vue";
import { useUserStore } from "@/stores/modules/user";

const userStore = useUserStore();
const innerUrl = computed(() => import.meta.env.VITE_BAILING_URL + `&Pid=${userStore.userInfo.pid}`);
console.log(innerUrl);
</script>
