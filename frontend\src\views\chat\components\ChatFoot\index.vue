<template>
  <div class="p-4 absolute left-0 right-0 bottom-0">
    <div
      class="chat-foot rounded-lg border border-gray-200 bg-white shadow-[0_0_10px_rgba(0,0,0,0.1)] hover:shadow-[0_0_15px_rgba(0,0,0,0.2)] transition-shadow duration-300"
    >
      <div>
        <!-- 引用 -->
        <div class="pt-4 mx-4 flex items-center text-sm" v-if="quoteMsg.id">
          <div class="px-4 py-1 border rounded textMaxWidth">
            {{ quoteMsg.senderName }}:
            <span v-html="quoteMsg.msg" v-if="quoteMsg.msgType == 8" class="w-8 h-8"></span>
            <span v-else v-html="quoteMsg.msg" style="word-break: break-all"></span>
          </div>
          <Icon :icon="closeCircleBoldDuotone" width="18" height="18" class="cursor-pointer ml-2" @click="clearText" />
        </div>
        <!-- @ -->
        <div class="flex flex-wrap mx-4 pt-4" v-if="atArr.length > 0">
          <span v-for="(item, index) in atArr" :key="index" class="flex items-center border rounded px-4 text-sm mr-2">
            @{{ item.memberName }}
            <Icon :icon="closeCircleBoldDuotone" width="18" height="18" class="cursor-pointer ml-2" @click="clearAt(index)" />
          </span>
        </div>
        <MemberList
          class="pt-4"
          :filteredUsers="filteredUsers"
          :activeIndex="activeIndex"
          v-if="showUserList"
          @select-user="onSelectUser"
        ></MemberList>
      </div>
      <div class="px-3 py-2">
        <el-input
          type="textarea"
          v-if="!fileShow && newMessage.msgType != 8"
          border="none"
          resize="none"
          @keydown="handleKeydown"
          autosize
          v-model="newMessage.msg"
          @keydown.enter="sendMessage(newMessage.secret)"
          placeholder="请输入您想发送的内容"
          ref="inputRef"
          maxlength="1000"
          show-word-limit
        />
      </div>
      <div class="flex items-center justify-between px-4 py-2">
        <div class="flex flex-wrap items-center space-x-2">
          <el-popover
            ref="popover"
            placement="top"
            :width="470"
            :popper-style="{ padding: '0', border: 'none', borderRadius: '8px' }"
            trigger="click"
          >
            <emoji-wrapper @emoji="onEmojiSelect" />
            <template #reference>
              <button class="foot-btn" title="添加表情">
                <Icon :icon="emojiSquareBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>

          <el-popover ref="popoverImg" placement="top" popper-class="custom-popper" trigger="click">
            <UploadImage :file-type="contact.chatType" @upload-img="handleUploadImg">
              <template #default="{ upload }">
                <div class="w-full flex flex-col items-center">
                  <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="upload(item.value)">
                    <LevelBtn :message="item.value" />
                  </div>
                </div>
              </template>
            </UploadImage>
            <template #reference>
              <button class="foot-btn" title="添加图片">
                <Icon :icon="galleryBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>

          <el-popover ref="popoverFile" placement="top" popper-class="custom-popper" trigger="click">
            <UploadFile :file-type="contact.chatType" @model-value="handleUploadFile">
              <template #default="{ upload }">
                <div class="w-full flex flex-col items-center">
                  <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="upload(item.value)">
                    <LevelBtn :message="item.value" />
                  </div>
                </div>
              </template>
            </UploadFile>
            <template #reference>
              <button class="foot-btn" title="添加文件">
                <Icon :icon="fileBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>

          <!-- <el-popover placement="top" popper-class="custom-popper" trigger="click">
            <div class="w-full flex flex-col items-center">
              <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="selectFile(item, 1)">
                <LevelBtn :message="item.value" />
              </div>
            </div>
            <template #reference>
              <button class="foot-btn" title="录制音频">
                <Icon :icon="microphoneBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover> -->

          <el-popover placement="top" popper-class="custom-popper" trigger="click" v-if="talkStore.activeContact.chatType">
            <div class="w-full flex flex-col items-center">
              <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="selectFile(item, 2)">
                <LevelBtn :message="item.value" />
              </div>
            </div>
            <template #reference>
              <button class="foot-btn" title="创建投票">
                <Icon :icon="chartSquareBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>

          <el-popover placement="top" popper-class="custom-popper" trigger="click" v-if="talkStore.activeContact.chatType">
            <div class="w-full flex flex-col items-center">
              <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="selectFile(item, 3)">
                <LevelBtn :message="item.value" />
              </div>
            </div>
            <template #reference>
              <button class="foot-btn" title="创建接龙">
                <Icon :icon="listBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>

          <el-popover placement="top" popper-class="custom-popper" trigger="click" v-if="talkStore.activeContact.chatType">
            <div class="w-full flex flex-col items-center">
              <div v-for="(item, index) in levelList" class="mb-pad" :key="index" @click="selectFile(item, 4)">
                <LevelBtn :message="item.value" />
              </div>
            </div>
            <template #reference>
              <button class="foot-btn" title="创建公文">
                <Icon :icon="bookBoldDuotone" width="21" height="21" />
              </button>
            </template>
          </el-popover>
        </div>
        <div class="flex items-center space-x-4">
          <div class="flex-1">
            <SecretLevelSlider v-model="newMessage.secret" :contact-secret="contactSecret" />
          </div>
          <button class="send-btn" @click="sendMessage(newMessage.secret)">
            <span class="flex items-center transition-all duration-200 rounded-md text-sm px-3 py-1.5">
              <Icon :icon="plainBoldDuotone" width="21" height="21" class="mr-2" />
              发送
            </span>
          </button>
        </div>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" width="300px" :close-on-click-modal="false" :show-close="false">
      <LevelBtn :message="newMessage.secret" class="cursor-pointer mb-1" />
      <AudioRecorder @modelval="getAuVal" :message="newMessage.secret" />
    </el-dialog>
    <VoteDialog ref="voteDialog" @send-message="handleSendMessage" />
    <RelayDialog ref="relayDialog" @send-message="handleSendMessage" />
    <OfficialDocument ref="offdocDialog" @send-message="handleSendMessage" />
  </div>
</template>

<script setup lang="ts">
import { ref, watchEffect, watch, computed } from "vue";
import EmojiWrapper from "@/components/EmojiComp/EmojiWrapper.vue";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useHistoryStore } from "@/stores/modules/history";
import { useRecentStore } from "@/stores/modules/recent";
import { useContactStore } from "@/stores/modules/friend";
import * as historyApi from "@/api/modules/history";
import LevelBtn from "@/components/LevelBtn/index.vue";
import { ElMessage } from "element-plus";
import UploadFile from "./UploadFile.vue";
import UploadImage from "./UploadImage.vue";
import AudioRecorder from "./AudioRecorder.vue";
import MemberList from "./MemberList.vue";
import VoteDialog from "@/components/ChatFootTool/VoteDialog.vue";
import RelayDialog from "@/components/ChatFootTool/RelayDialog.vue";
import OfficialDocument from "@/components/ChatFootTool/OfficialDocument.vue";
import { MessageCode, MessageSendInfo, ContentType } from "@/utils/websocket/messageInfo";
import { SecretLevelConverter } from "@/utils/secretLevelConverter";
import WebSocketClient from "@/utils/websocket/websocketClient";
import * as dbApi from "@/utils/indexedDB";
import SecretLevelSlider from "@/components/SecretLevelSlider/index.vue";
import { Icon } from "@iconify/vue";

// 导入离线图标
import closeCircleBoldDuotone from "@iconify-icons/solar/close-circle-bold-duotone";
import emojiSquareBoldDuotone from "@iconify-icons/solar/emoji-funny-square-bold-duotone";
import galleryBoldDuotone from "@iconify-icons/solar/gallery-bold-duotone";
import fileBoldDuotone from "@iconify-icons/solar/file-bold-duotone";
import microphoneBoldDuotone from "@iconify-icons/solar/microphone-bold-duotone";
import chartSquareBoldDuotone from "@iconify-icons/solar/chart-square-bold-duotone";
import listBoldDuotone from "@iconify-icons/solar/list-bold-duotone";
import bookBoldDuotone from "@iconify-icons/solar/book-bold-duotone";
import plainBoldDuotone from "@iconify-icons/solar/plain-bold-duotone";
import { ipc, isEE } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";

const userStore = useUserStore();
const historyStore = useHistoryStore();
const talkStore = useTalkStore();
const recentStore = useRecentStore();
const contactStore = useContactStore();

// 投票
const voteDialog: any = ref(null);
const openVoteDialog = () => {
  if (voteDialog.value) voteDialog.value.openDialog();
};
// 接龙
const relayDialog: any = ref(null);
const openRelayDialog = () => {
  if (relayDialog.value) relayDialog.value.openDialog();
};
// 引用
const quoteMsg: any = computed(() => talkStore.quoteMsg);

// 公文
const offdocDialog: any = ref(null);
const openOffdocDialog = () => {
  if (offdocDialog.value) offdocDialog.value.openDialog();
};

const popoverRefs = ref([]);

const atArr: any = ref([]);
const atStatus = ref(true);
const fileShow = ref(false);
const inputRef: any = ref(null);

const popover = ref<any>(null);
const popoverImg = ref<any>(null);
const popoverFile = ref<any>(null);
const dialogVisible = ref(false);
// 初始化newMessage
const newMessage: any = ref({});
const initNewMsg = () => {
  newMessage.value = {
    sender: userStore.userId,
    senderName: userStore.name,
    receiver: contact.value.contactId,
    receiverName: contact.value.contactName,
    secret: 0,
    msg: "",
    msgType: 0,
    quoteId: "",
    atUserIds: "",
    contentId: ""
  };
  atArr.value = [];
  atStatus.value = true;
  fileShow.value = false;
};
const contact: any = ref({});
const contactSecret = ref(30);
const levelList: any = ref([]);
watchEffect(() => {
  contact.value = talkStore.activeContact;
  contactSecret.value = Math.min(contact.value.secret, userStore.secretLevel);
  levelList.value = SecretLevelConverter.getOptions("obj").filter((item: any) => item.value <= contactSecret.value);
  initNewMsg();
});

const activeIndex = ref(-1);
const showUserList = ref(false);

/*
  msgType: 4.投票  5.接龙  9.公文
 */
const handleSendMessage = (message: string, msgType: number) => {
  newMessage.value.msgType = msgType;
  newMessage.value.msg = message.toString();
  newMessage.value.contentId = message.toString();
  sendMessage(newMessage.value.secret);
};

const getAuVal = (val: any, item?: any) => {
  dialogVisible.value = val;
  // 上传音频
  if (item) {
    newMessage.value.msgType = 3;
    newMessage.value.msg = userStore.userId + "-" + Math.floor(Date.now() / 1000).toString();
    newMessage.value.contentId = item;
    sendMessage(newMessage.value.secret);
  }
};

// 上传图片关闭密级选择框
const handleUploadImg = () => {
  if (timer) clearTimeout(timer);
  if (popoverImg.value) popoverImg.value.hide();
};
// 上传文件关闭密级选择框
const handleUploadFile = () => {
  if (timer) clearTimeout(timer);
  if (popoverFile.value) popoverFile.value.hide();
};

// 清除引用消息
const clearText = () => {
  talkStore.setQuoteMsg();
};

// 语音，投票，接龙, 公文
const selectFile = (val: any, num: any) => {
  newMessage.value.secret = val.value;
  if (timer) clearTimeout(timer);
  if (num == 1) {
    dialogVisible.value = true;
    return;
  } else if (num == 2) {
    openVoteDialog();
  } else if (num == 3) {
    openRelayDialog();
  } else if (num == 4) {
    openOffdocDialog();
  }

  popoverRefs.value.forEach((popoverRef: any) => {
    if (popoverRef) popoverRef.hide();
  });
};
// @相关功能开始 ************************************
const dataArr = computed(() => {
  return contactStore.groupMembers[talkStore.activeChatId];
});
const filteredUsers = computed(() => {
  if (!showUserList.value) return [];
  const query = newMessage.value.msg.match(/@(\w*)$/)?.[1] || "";
  return dataArr.value.filter((user: any) => user.memberName.toLowerCase().includes(query));
});
const handleKeydown = (event: any) => {
  if (event.key === "@" && contact.value.chatType != 0) {
    showUserList.value = true;
  }
};
// 选择群组@人员
const onSelectUser = (user: any) => {
  const atIndex = newMessage.value.msg.lastIndexOf("@");
  const beforeAt = newMessage.value.msg.substring(0, atIndex);
  newMessage.value.msg = `${beforeAt}`;
  activeIndex.value = -1;
  atArr.value.push(user);
  if (inputRef.value) {
    inputRef.value.focus();
  }
  showUserList.value = false;
};
// 删除@选中人员
const clearAt = (num: any) => {
  atArr.value.splice(num, 1);
};

// @相关功能结束 **************************************

/** 处理表情选择事件 */
const onEmojiSelect = ({ type, content }: any) => {
  if (timer) clearTimeout(timer);
  switch (type) {
    case "insert":
      if (newMessage.value.msg.length > 991) {
        ElMessage.warning("当前字符数已达最大限制，不能添加表情");
        return;
      } else {
        insertText(content);
      }
      break;
    case "send":
      newMessage.value.msgType = 8;
      newMessage.value.msg = content;
      newMessage.value.secret = 30;
      sendMessage(30);
      break;
  }
  if (popover.value) popover.value.hide();
  if (inputRef.value) {
    inputRef.value.focus();
  }
};
// 将表情插入文本
const insertText = (content: any) => {
  const textarea = document.querySelector("textarea");
  if (!textarea) return;
  // 获取光标位置
  const start = textarea.selectionStart;
  const end = textarea.selectionEnd;
  // 要插入的文本
  const toInsert = content;
  // 更新文本内容，同时保持其他部分不变
  newMessage.value.msg = newMessage.value.msg.substring(0, start) + toInsert + newMessage.value.msg.substring(end);
  // 移动光标位置
  textarea.focus();
  textarea.setSelectionRange(start + toInsert.length, start + toInsert.length);
};
// 过滤敏感词
const filterFun = (str: any) => {
  return talkStore.filterWords.some((item: any) => str.includes(item.word));
};
// 转义 HTML 特殊字符
const escapeHtml = (unsafe: any) => {
  return unsafe.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");
};
const webSocketManager = WebSocketClient.getInstance(); // 无需参数
let timer: NodeJS.Timeout | null = null; // 定时器

const sendMessage = async (secretLevel: any) => {
  if (!newMessage.value.msg.trim()) {
    ElMessage.warning("请输入您想发送的内容");
    return;
  }
  if (!secretLevel) {
    ElMessage.warning("请选择密级！");
    return;
  }
  if (filterFun(newMessage.value.msg)) {
    ElMessage.warning("内容包含敏感词汇，请修改后再发！");
    return;
  }
  if (quoteMsg.value.id) {
    newMessage.value.quoteId = quoteMsg.value.id;
  }
  let res: any = {};
  if (newMessage.value.msgType != ContentType.EMOJI) newMessage.value.msg = escapeHtml(newMessage.value.msg);
  if (contact.value.chatType != 0) {
    atStatus.value = false;
    newMessage.value.atUserIds = atArr.value.map((item: any) => item.member).toString();
    if (atArr.value.length > 0) {
      newMessage.value.msg =
        atArr.value
          .map((item: any) => "@" + item.memberName)
          .toString()
          .replace(/,/g, " ") +
        " " +
        newMessage.value.msg;
    }
    try {
      res = await historyApi.saveGroupMessage(newMessage.value);
    } catch (error) {
      dealingMessage(error, newMessage.value);
      return;
    }
  } else {
    try {
      res = await historyApi.saveUserMessage(newMessage.value);
    } catch (error) {
      dealingMessage(error, newMessage.value);
      return;
    }
  }
  dealingMessage(res, newMessage.value);
};

const dealingMessage = (res: any, sendData: any) => {
  let addMsg = {} as any;
  if (res?.code == 0) {
    addMsg = res.data;

    let msg: MessageSendInfo = {
      code: contact.value.chatType ? MessageCode.GROUP : MessageCode.PRAIVTE,
      data: {
        id: addMsg.id,
        fromId: addMsg.sender,
        toId: addMsg.receiver,
        atId: atArr.value.map((item: any) => item.member),
        isGroup: contact.value.chatType ? true : false,
        contentId: addMsg.contentId,
        chatType: contact.value.chatType,
        content: {
          msg: addMsg.msg,
          time: addMsg.createTime,
          secret: addMsg.secret,
          quoteSenderName: addMsg.quoteSenderName,
          quoteContent: addMsg.quoteContent,
          quoteMessageFile: addMsg.quoteMessageFile
        },
        contentType: addMsg.msgType
      }
    };
    webSocketManager.send(msg);

    // 存储消息数据到indexedDB
    let message = {
      contact: {
        avatar: contact.value.avatar,
        chatType: contact.value.chatType,
        contactId: contact.value.contactId,
        contactName: contact.value.contactName,
        secret: contact.value.secret
      },
      msgId: addMsg.id,
      msg: addMsg.msg
    };
    dbApi.addData(message);
  } else if (sendData.msgType == 0 || sendData.msgType == 8) {
    const currentTime = new Date().getTime();
    // isOfflineMsgType 1.接口发送失败 2.ws发送失败
    addMsg = {
      ...sendData,
      offlineMsgFailedType: 1,
      id: "offlineMsg_" + currentTime,
      createTime: currentTime,
      updateTime: currentTime,
      creator: sendData.sender,
      updater: sendData.sender,
      cancel: 0,
      deleteFlag: 1,
      waitData: sendData
    };
    // 离线消息都做如下处理
    historyStore.setMsgHistory(addMsg.receiver, addMsg.id, addMsg);
    recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
  }

  if (contact.value.chatType == 0) {
    historyStore.setMsgHistory(addMsg.receiver, addMsg.id, addMsg);
    recentStore.updateLastMsg(addMsg.receiver, addMsg.msg, addMsg.id, addMsg.msgType, addMsg.updateTime);
  }
  recentStore.updateOrder(addMsg.receiver);

  talkStore.setStatus(true);
  // 存储消息数据到客户端
  if (isEE) {
    let ipcMsg = JSON.stringify(addMsg);
    if (contact.value.chatType) {
      ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
    } else {
      ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
    }
  }
  const keepSecret = newMessage.value.secret;

  initNewMsg();
  clearText();

  // 一定时间内保持密级不变
  newMessage.value.secret = keepSecret;
  if (timer) clearTimeout(timer);
  timer = setTimeout(() => {
    initNewMsg();
  }, 15 * 1000);
};

watch(
  () => newMessage.value.msg,
  newMsg => {
    if (!newMsg && !newMsg.includes("@")) showUserList.value = false;
  }
);

watch(
  () => talkStore.activeChatId,
  _newVal => {
    if (timer) clearTimeout(timer);
  }
);
</script>

<style scoped lang="scss">
.conwide {
  width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.textMaxWidth {
  max-width: 95%;
}
.mb-pad {
  @apply mb-2;
}

.mb-pad:last-child {
  @apply mb-0;
}

.chat-foot {
  background-color: #ffffff;
  position: relative;
  z-index: 999;

  :deep(.el-textarea) {
    @apply text-sm;

    .el-textarea__inner {
      @apply shadow-none;
      background-color: transparent !important;
      border: none !important;

      &::placeholder {
        color: #9ca3af;
      }
    }
  }

  .foot-btn {
    @apply inline-flex cursor-pointer justify-center rounded-lg p-2 text-gray-600 hover:bg-gray-100 hover:text-gray-900;
  }

  .el-radio {
    @apply mr-0;

    .el-radio__label span {
      @apply cursor-pointer rounded-md px-2 py-0.5 text-xs font-medium;

      &.feimi {
        @apply text-green-600 bg-green-100;
      }

      &.mimi {
        @apply text-yellow-600 bg-yellow-100;
      }

      &.jimi {
        @apply text-orange-600 bg-orange-100;
      }
    }
  }

  .send-btn {
    @apply flex h-min items-center justify-center p-0.5 text-center font-medium relative focus:z-10 focus:outline-none text-white bg-blue-700 hover:bg-blue-800 focus:ring-blue-300 rounded-lg focus:ring-2;
  }
}

.dark {
  .chat-foot {
    @apply border-gray-700 bg-transparent;
    background-color: transparent !important;

    .foot-btn {
      @apply text-gray-400 hover:bg-gray-700 hover:text-white;
    }

    :deep(.el-textarea) {
      .el-textarea__inner {
        background-color: transparent !important;
        border: none !important;
        color: white !important;

        &::placeholder {
          color: #6b7280;
        }
      }
    }

    .el-radio .el-radio__label span {
      &.feimi {
        @apply bg-green-900/30 text-green-500;
      }

      &.mimi {
        @apply bg-yellow-900/30 text-yellow-500;
      }

      &.jimi {
        @apply bg-orange-900/30 text-orange-500;
      }
    }

    .send-btn {
      @apply bg-blue-600 hover:bg-blue-700 focus:ring-blue-800;
    }
  }
}
</style>
