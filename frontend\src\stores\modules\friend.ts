import { defineStore } from "pinia";
import { Contact } from "../interface/index";
import piniaPersistConfig from "../helper/persist";
import * as contactApi from "@/api/modules/contact";
import { useRecentStore } from "./recent";
import { useTalkStore } from "./talk";
import { useUserStore } from "./user";
import { useContactsStore } from "./contacts";

const recentStore = useRecentStore();
const talkStore = useTalkStore();
const userStore = useUserStore();
const contactsStore = useContactsStore();

export const useContactStore = defineStore("lark-groupInfo", {
  state: (): Contact => ({
    groupInfo: {},
    groupMembers: {}
  }),
  getters: {},
  actions: {
    // 获取通讯录时更新群组成员数量
    updateGroupMemberInfo(data: any[]) {
      data.map(item => {
        if (this.groupInfo[item.id]) {
          this.groupInfo[item.id] = item.member;
        }
      });
    },
    // 获取群组信息
    async getGroupInfo(id: string) {
      const { data } = await contactApi.queryGroupInfo(id);
      this.groupInfo[id] = data;
    },
    // 获取群组成员
    async getGroupMember(id: string) {
      const { data } = await contactApi.listMemberByGroupId(id);

      if (data) {
        data?.forEach((item: any) => {
          let fileds: any = ["avatar", "isOnline"];
          if (item.online === null) fileds = ["avatar"];
          // 更新用户状态
          contactsStore.updateContact(
            {
              id: item.member,
              name: item.memberName,
              avatar: item.avatar,
              secretLevel: item.secretLevel,
              isOnline: item.online == "on"
            },
            fileds
          );
        });
        
        contactsStore.updateSqliteData();
      }
      this.groupMembers[id] = data;
    },
    // 解散群组
    deleteGroup(groupId: string) {
      const index = recentStore.listRecents.findIndex((item: any) => item.contactId == groupId);
      if (index > -1) recentStore.deleteListRecents(recentStore.listRecents[index].id);

      if (this.groupInfo[groupId]) delete this.groupInfo[groupId];
      if (this.groupMembers[groupId]) delete this.groupMembers[groupId];
    },
    // 离开群组
    leaveGroup(groupId: string) {
      this.deleteGroup(groupId);
      if (groupId == talkStore.activeChatId) {
        talkStore.setActiveChat("");
        talkStore.ifChat = false;
      }
    },
    // 修改群组信息
    updateGroupInfo(id: string, groupName?: any, groupNotice?: any, ownerId?: any, ownerName?: any) {
      if (groupName) this.groupInfo[id].groupName = groupName;
      if (groupNotice) this.groupInfo[id].groupNotice = groupNotice;
      if (ownerId) {
        this.groupInfo[id].groupOwnerId = ownerId;
        this.groupInfo[id].groupOwnerName = ownerName;
      }
    },
    // 删除群组成员
    deleteGroupMember(id: string, ids: string, userList?: any[]) {
      let arr = ids.split(",");
      arr.forEach(member => {
        let index = this.groupMembers[id].findIndex((item: { id: string }) => item.id == member);
        this.groupMembers[id].splice(index, 1);
      });

      userList?.forEach(item => {
        if (item.userId == userStore.userId || item.member == userStore.userId) {
          this.leaveGroup(id);
        }
      });
    }
  },
  persist: piniaPersistConfig("lark-groupInfo-session")
});
