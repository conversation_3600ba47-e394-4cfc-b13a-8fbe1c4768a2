<template>
  <div class="search-drawer">
    <el-dialog v-model="visible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'add']" class="icon" />
          <span class="title">添加好友</span>
        </div>
      </template>
      <div class="rounded-xl">
        <div class="mb-2">
          <el-input v-model="searchData" ref="searchInput" placeholder="请输入联系人名称" @keydown.enter="getSearchResultsFn">
            <template #suffix>
              <font-awesome-icon :icon="['fas', 'search']" class="cursor-pointer text-xl" @click="getSearchResultsFn" />
            </template>
          </el-input>
        </div>
        <el-scrollbar>
          <FriendContact :user="resultSingleOuter" @click="handleClick" @search-click="handleClick" />
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getChatSingle, getAllFriend } from "@/api/modules/contact";
import { useUserStore } from "@/stores/modules/user";
import FriendContact from "@/components/ContactComp/FriendContact.vue";
import { useContactsStore } from "@/stores/modules/contacts";

const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const contactsStore = useContactsStore();

const searchInput: any = ref(null);
const searchData = ref("");
const resultSingleOuter = ref([]);

const visible = ref(false);
const openDialog = () => {
  visible.value = true;
  if (friendList.value.length == 0) {
    getFriendList();
  }
  getSearchResultsFn();
};
const closeDialog = () => {
  visible.value = false;
  searchData.value = "";
  resultSingleOuter.value = [];
};
defineExpose({ openDialog });

const friendList: any = ref([]);
const getFriendList = async () => {
  const params = {
    userId: userStore.userId,
    status: "1"
  };
  const res: any = await getAllFriend(params);
  friendList.value = res.data;
};

const searchSingle = async () => {
  if (!searchData.value) return;
  const params = {
    name: searchData.value
  };
  const { data } = await getChatSingle(params);
  let arr = data.list.filter((item: any) => item.isExternal == "1");
  let obj: any = {};
  resultSingleOuter.value = arr.map((item: any) => {
    if (friendList.value.some((item_: any) => item.id == item_.id)) {
      obj.ifOuter = true;
    } else {
      obj.ifOuter = false;
    }

    let fileds: any = ["avatar", 'isOnline']
    if (item.online === null) fileds = ["avatar"]
    // 更新用户状态
    contactsStore.updateContact(
      {
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        secretLevel: item.secretLevel,
        isOnline: item.online == "on"
      },
      fileds
    );

    return {
      ...item,
      ...obj
    };
  });
  
  contactsStore.updateSqliteData();
};
const getSearchResultsFn = async () => {
  await searchSingle();
};
const handleClick = () => {
  closeDialog();
  if (!route.path.includes("chat")) {
    router.push({ name: "chat" });
  }
};
</script>

<style lang="scss" scoped>
.search-drawer {
  :deep(.el-collapse) {
    @apply border-0;
    .el-collapse-item__content {
      @apply pb-2;
    }
  }

  :deep(.el-scrollbar) {
    height: 400px;
  }
}

.dark {
  .msg-item {
    @apply hover:bg-gray-700;
    h4 {
      @apply text-white;
    }
    p {
      @apply text-gray-400;
    }
  }
}
</style>
