<template>
  <div 
    ref="noteManagerContainer"
    class="note-manager-container"
    :class="{ 'dark': isDarkMode, 'light': !isDarkMode }"
    :data-theme="isDarkMode ? 'dark' : 'light'"
  >
    <!-- 窗口控制栏 -->
    <div class="window-control-bar">
      <div class="window-header-content">
        <div class="window-header-left">
          <div class="app-icon">
            <Icon :icon="fountainPenIcon" width="20" height="20" />
          </div>
          <h3 class="window-title">AI笔记</h3>
        </div>
        <div class="window-header-actions">
          <button @click="toggleTheme" class="control-btn theme-btn" title="切换主题">
            <font-awesome-icon :icon="isDarkMode ? 'sun' : 'moon'" />
          </button>
          <button @click="minimizeWindow" class="control-btn minimize-btn" title="最小化">
            <font-awesome-icon icon="minus" />
          </button>
          <button @click="toggleMaximizeWindow" class="control-btn maximize-btn" title="最大化/还原">
            <font-awesome-icon :icon="isMaximized ? 'window-restore' : 'square'" />
          </button>
          <button @click="closeWindow" class="control-btn close-btn" title="关闭">
            <font-awesome-icon icon="xmark" />
          </button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧笔记列表 -->
      <div class="note-list-panel">
        <div class="panel-header">
          <!-- 搜索栏 -->
          <div class="search-input-wrapper">
            <font-awesome-icon icon="magnifying-glass" class="search-icon" />
            <input 
              v-model="searchKeyword" 
              placeholder="搜索笔记..." 
              class="search-input"
              @input="handleSearch"
            />
            <button v-if="searchKeyword" @click="clearSearch" class="clear-btn">
              <font-awesome-icon icon="xmark" />
            </button>
          </div>
          <button class="action-btn primary" @click="openNoteEditor">
            <font-awesome-icon icon="plus" />
          </button>
        </div>
        
        <!-- 笔记列表 -->
        <div class="note-list">
          <div v-if="loading" class="loading-container">
            <div class="loading-spinner"></div>
            <p class="loading-text">加载中...</p>
          </div>
          
          <div v-else-if="filteredNotes.length === 0" class="empty-state">
            <font-awesome-icon icon="file-lines" size="3x" class="empty-icon" />
            <p class="empty-text">{{ searchKeyword ? '没有找到相关笔记' : '暂无笔记' }}</p>
            <button v-if="!searchKeyword" @click="openNoteEditor" class="empty-action-btn">
              <font-awesome-icon icon="plus" />
              创建第一条笔记
            </button>
          </div>
          
          <div v-else class="note-items">
            <div 
              v-for="note in filteredNotes" 
              :key="note.id"
              :class="['note-item', { active: selectedNote?.id === note.id }]"
              @click="selectNote(note)"
            >
              <div class="note-content">
                <h4 class="note-title">{{ note.title || note.content.substring(0, 30) + '...' }}</h4>
                <p class="note-preview">{{ note.content.substring(0, 80) }}{{ note.content.length > 80 ? '...' : '' }}</p>
                <div class="note-meta">
                  <span class="note-time">{{ formatTime(note.updateTime) }}</span>
                  <span v-if="note.noteType" class="note-type">{{ getNoteTypeLabel(note.noteType) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容面板 -->
      <div class="note-content-panel">
        <!-- 编辑器模式 -->
        <div v-if="showEditor" class="editor-panel">
          <InlineNoteEditor
            :mode="editorMode"
            :note-id="editingNote?.id"
            :initial-content="editingNote?.originalContent || editingNote?.content || ''"
            :initial-title="editingNote?.title || ''"
            @save="handleEditorSave"
            @cancel="handleEditorCancel"
          />
        </div>

        <!-- 预览模式 -->
        <div v-else class="preview-panel">
          <div v-if="!selectedNote" class="no-selection">
            <font-awesome-icon icon="mouse-pointer" size="4x" class="select-icon" />
            <p class="select-text">选择一条笔记查看详情</p>
          </div>

          <div v-else class="note-detail">
            <div class="detail-header">
              <h2 class="detail-title">{{ selectedNote.title || '无标题' }}</h2>
              <div class="detail-actions">
                <button class="detail-btn" @click="editNote" title="编辑">
                  <font-awesome-icon icon="pencil" />
                </button>
                <button class="detail-btn" @click="exportNote" title="导出">
                  <font-awesome-icon icon="download" />
                </button>
                <button class="detail-btn danger" @click="deleteNote" title="删除">
                  <font-awesome-icon icon="trash" />
                </button>
              </div>
            </div>

            <div class="detail-meta">
              <div class="meta-item" style="align-items: center;">
                <span class="meta-label">更新时间:</span>
                <span class="meta-value" style="flex-grow: 1;">{{ formatTime(selectedNote.updateTime) }}</span>
                <span v-if="selectedNote.noteType" class="note-type">{{ getNoteTypeLabel(selectedNote.noteType) }}</span>
              </div>
              <div v-if="selectedNote.tags && selectedNote.tags.length > 0" class="meta-item">
                <span class="meta-label">标签:</span>
                <div class="tag-list">
                  <span v-for="tag in selectedNote.tags" :key="tag" class="tag">{{ tag }}</span>
                </div>
              </div>
            </div>

            <div class="detail-content">
              <div class="detail-content-wrapper">
                <InlineNoteEditor
                  v-if="selectedNote"
                  :key="selectedNote.id"
                  :readOnly="true"
                  :initial-content="selectedNote.originalContent || selectedNote.content"
                  :initial-title="selectedNote.title"
                />
              </div>
            </div>

            <div v-if="selectedNote.summary" class="detail-summary">
              <h4 class="summary-title">AI摘要</h4>
              <p class="summary-text">{{ selectedNote.summary }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { Icon } from '@iconify/vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import dayjs from 'dayjs'
import { ipc } from '@/utils/ipcRenderer'
import { ipcApiRoute } from '@/ipc/ipcApi'
import { library } from '@fortawesome/fontawesome-svg-core'
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'
import { 
  faMinus, faSquare, faXmark, faMagnifyingGlass, faPlus, faFileLines, 
  faMousePointer, faPencil, faDownload, faTrash, faWindowRestore, faSun, faMoon 
} from '@fortawesome/free-solid-svg-icons'
// 移除后端API导入，只使用本地数据源
import InlineNoteEditor from './InlineNoteEditor.vue'

// 导入图标
import fountainPenIcon from '@iconify-icons/fluent-emoji/fountain-pen'

library.add(
  faMinus, faSquare, faXmark, faMagnifyingGlass, faPlus, faFileLines, 
  faMousePointer, faPencil, faDownload, faTrash, faWindowRestore, faSun, faMoon
)

interface Note {
  id: number
  title: string
  content: string
  originalContent?: string
  summary?: string
  noteType: string
  category?: string
  tags?: string[]
  time: string
  wordCount?: number
  createTime: string
  updateTime?: string
}

interface DbNote {
  id: number
  content: string
  original_content?: string
  created_at: string
  title?: string
}

const notes = ref<Note[]>([])
const loading = ref(false)
const searchKeyword = ref('')
const selectedNoteId = ref<number | null>(null)
const selectedNote = ref<Note | null>(null)

// 编辑器相关状态
const showEditor = ref(false)
const editorMode = ref<'create' | 'edit'>('create')
const editingNote = ref<Note | null>(null)
const isMaximized = ref(false)

// 主题相关状态
const isDarkMode = ref(false)
const noteManagerContainer = ref<HTMLElement | null>(null)

// 初始化主题
const initTheme = () => {
  const savedTheme = localStorage.getItem('note-manager-theme')
  const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  
  isDarkMode.value = savedTheme ? savedTheme === 'dark' : prefersDark
  
  // 使用 nextTick 确保 DOM 已渲染
  nextTick(() => {
    applyTheme()
  })
}

// 应用主题
const applyTheme = () => {
  // 使用 ref 获取容器元素
  const container = noteManagerContainer.value
  if (!container) {
    console.warn('Note Manager container not found')
    return
  }
  
  // 移除现有的主题类
  container.classList.remove('dark', 'light')
  
  if (isDarkMode.value) {
    container.classList.add('dark')
    container.setAttribute('data-theme', 'dark')
  } else {
    container.classList.add('light')
    container.setAttribute('data-theme', 'light')
  }
  
  // 触发自定义事件，通知其他组件主题已更改（仅限于当前组件内部）
  container.dispatchEvent(new CustomEvent('note-manager-theme-changed', { 
    detail: { isDark: isDarkMode.value },
    bubbles: false
  }))
  
  console.log('Note Manager theme applied:', isDarkMode.value ? 'dark' : 'light', 'Container classes:', container.classList.toString())
}

// 切换主题
const toggleTheme = () => {
  // 添加主题切换动画
  const themeBtn = document.querySelector('.theme-btn')
  if (themeBtn) {
    themeBtn.classList.add('theme-switching')
    setTimeout(() => {
      themeBtn.classList.remove('theme-switching')
    }, 800)
  }

  isDarkMode.value = !isDarkMode.value
  localStorage.setItem('note-manager-theme', isDarkMode.value ? 'dark' : 'light')
  applyTheme()
  
  // 添加用户反馈
  ElMessage.success(`已切换到${isDarkMode.value ? '深色' : '浅色'}主题`)
  
  // 强制刷新样式
  setTimeout(() => {
    const container = document.querySelector('.note-manager-container')
    if (container) {
      container.classList.add('theme-switching')
      setTimeout(() => {
        container.classList.remove('theme-switching')
      }, 400)
    }
  }, 50)
}

// 计算属性
const filteredNotes = computed(() => {
  if (!searchKeyword.value) return notes.value
  
  const keyword = searchKeyword.value.toLowerCase()
  return notes.value.filter(note => 
    note.title.toLowerCase().includes(keyword) ||
    note.content.toLowerCase().includes(keyword) ||
    (note.summary && note.summary.toLowerCase().includes(keyword))
  )
})

// 方法
const loadNotes = async () => {
  loading.value = true;
  selectedNote.value = null;
  selectedNoteId.value = null;

  try {
    const result: DbNote[] = await ipc.invoke(ipcApiRoute.getAllNotes)
    notes.value = result.map(n => ({
      id: n.id,
      title: n.title || n.content.substring(0, 20) + '...',
      content: n.content,
      originalContent: n.original_content,
      noteType: 'quick',
      time: dayjs(n.created_at).format('YYYY-MM-DD HH:mm'),
      createTime: n.created_at,
      updateTime: n.created_at
    }))
  } catch (error) {
    console.error('本地获取笔记失败:', error)
    notes.value = []
    ElMessage.error('加载笔记失败')
  } finally {
    loading.value = false
  }
}



const openNoteEditor = () => {
  // 切换到编辑器模式，新建笔记
  showEditor.value = true
  editorMode.value = 'create'
  editingNote.value = null
  selectedNote.value = null
  selectedNoteId.value = null
}

const selectNote = (note: Note) => {
  selectedNoteId.value = note.id;
  selectedNote.value = note;
};

const deleteNote = async () => {
  if (!selectedNote.value) return

  try {
    await ElMessageBox.confirm(
      '确定要删除这条笔记吗？删除后无法恢复。',
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await ipc.invoke(ipcApiRoute.deleteNote, selectedNote.value.id)

    ElMessage.success('删除成功')

    // 重新加载笔记列表
    await loadNotes()

    // 清空选中状态
    selectedNote.value = null;
    selectedNoteId.value = null;
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

const editNote = () => {
  // 确保有选中的笔记
  if (!selectedNote.value) return;

  // 切换到编辑器模式，编辑当前笔记
  showEditor.value = true
  editorMode.value = 'edit'
  editingNote.value = selectedNote.value
};

// 编辑器事件处理
const handleEditorSave = async (data: { id?: number; content: string; title: string; originalContent: string }) => {
  await loadNotes()

  // 如果是编辑模式，更新选中的笔记
  if (editorMode.value === 'edit' && data.id) {
    const updatedNote = notes.value.find(note => note.id === data.id)
    if (updatedNote) {
      selectedNote.value = updatedNote
      selectedNoteId.value = updatedNote.id
    }
  } else if (data.id) {
    const newNote = notes.value.find(note => note.id === data.id);
    if (newNote) {
      selectedNote.value = newNote;
      selectedNoteId.value = newNote.id;
    }
  }

  // 切换回预览模式
  showEditor.value = false
  editorMode.value = 'create'
  editingNote.value = null
}

const handleEditorCancel = () => {
  // 切换回预览模式
  showEditor.value = false
  editorMode.value = 'create'
  editingNote.value = null
}

const exportNote = async () => {
  if (!selectedNote.value) return

  try {
    // 显示导出格式选择对话框
    const format = await ElMessageBox.confirm(
      '请选择导出格式',
      '导出笔记',
      {
        confirmButtonText: 'Word文档',
        cancelButtonText: 'Markdown',
        distinguishCancelAndClose: true,
        type: 'info'
      }
    ).then(() => 'word').catch((action) => {
      if (action === 'cancel') {
        return 'markdown'
      }
      throw action
    })

    if (format === 'word') {
      await exportToWord()
    } else {
      await exportToMarkdown()
    }

  } catch (error: any) {
    if (error !== 'cancel' && error !== 'close') {
      console.error('导出失败:', error)
      ElMessage.error('导出失败')
    }
  }
}

// 导出为Markdown格式
const exportToMarkdown = async () => {
  if (!selectedNote.value) return

  // 创建导出内容
  const exportContent = `# ${selectedNote.value.title || '无标题'}\n\n${selectedNote.value.content}\n\n---\n创建时间: ${formatTime(selectedNote.value.createTime)}\n更新时间: ${formatTime(selectedNote.value.updateTime)}`

  // 创建下载链接
  const blob = new Blob([exportContent], { type: 'text/markdown;charset=utf-8' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${selectedNote.value.title || '笔记'}.md`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  ElMessage.success('笔记已导出为Markdown格式')
}

// 导出为Word格式
const exportToWord = async () => {
  if (!selectedNote.value) return

  try {
    // 动态导入所需的库
    const [
      { writeDocx, DocxSerializer, defaultNodes, defaultMarks },
      { saveAs }
    ] = await Promise.all([
      import('prosemirror-docx'),
      import('file-saver')
    ])

    // 创建一个临时的Tiptap编辑器实例来解析HTML内容
    const { Editor } = await import('@tiptap/core')
    const StarterKit = await import('@tiptap/starter-kit')

    // 创建临时编辑器
    const tempEditor = new Editor({
      extensions: [StarterKit.default],
      content: selectedNote.value.originalContent || `<p>${selectedNote.value.content}</p>`,
    })

    // 配置节点序列化器
    const nodeSerializer = {
      ...defaultNodes,
      hardBreak: defaultNodes.hard_break,
      codeBlock: defaultNodes.code_block,
      orderedList: defaultNodes.ordered_list,
      listItem: defaultNodes.list_item,
      bulletList: defaultNodes.bullet_list,
      horizontalRule: defaultNodes.horizontal_rule,
      image(state: any, node: any) {
        // 暂不处理图片
        state.renderInline(node)
        state.closeBlock(node)
      }
    }

    // 创建DOCX序列化器
    const docxSerializer = new DocxSerializer(nodeSerializer, defaultMarks)

    // 序列化选项
    const opts = {
      getImageBuffer(_src: string) {
        // 这里可以处理图片，暂时返回空buffer
        return Buffer.from('')
      }
    }

    // 序列化文档
    const wordDocument = docxSerializer.serialize(tempEditor.state.doc, opts)

    // 写入并下载文件
    await writeDocx(wordDocument, (buffer: any) => {
      saveAs(new Blob([buffer]), `${selectedNote.value?.title || '笔记'}.docx`)
    })

    // 清理临时编辑器
    tempEditor.destroy()

    ElMessage.success('笔记已导出为Word格式')
  } catch (error) {
    console.error('Word导出失败:', error)
    ElMessage.error('Word导出失败，请重试')
  }
}

const handleSearch = () => {
  // 搜索逻辑已通过计算属性实现
}

const clearSearch = () => {
  searchKeyword.value = ''
}

const getNoteTypeLabel = (type: string) => {
  const typeMap: Record<string, string> = {
    'quick': '闪记',
    'summary': '摘要',
    'note': '笔记',
    'idea': '想法',
    'todo': '待办'
  }
  return typeMap[type] || type
}

// 窗口控制方法
const minimizeWindow = () => {
  ipc.invoke(ipcApiRoute.minimizeNoteManagerWindow)
}

const toggleMaximizeWindow = async () => {
  try {
    await ipc.invoke(ipcApiRoute.toggleMaximizeNoteManagerWindow)
  } catch (error) {
    console.error('[Frontend] Toggle maximize error:', error)
  }
}

const closeWindow = () => {
  ipc.invoke(ipcApiRoute.closeNoteManagerWindow)
}

// 时间格式化
const formatTime = (time: string | undefined) => {
  if (!time) return '未知时间'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}



// 生命周期
onMounted(() => {
  // 初始化主题
  initTheme()
  
  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleSystemThemeChange = (e: MediaQueryListEvent) => {
    // 只有在用户没有手动设置主题偏好时才跟随系统主题
    const savedTheme = localStorage.getItem('note-manager-theme')
    if (!savedTheme) {
      isDarkMode.value = e.matches
      applyTheme()
    }
  }
  
  mediaQuery.addEventListener('change', handleSystemThemeChange)
  
  loadNotes()

  ipc.on('window-maximized-state', (_event: any, state: boolean) => {
    isMaximized.value = state
  })

  // 监听本地笔记保存事件
  ipc.on('quick-note-saved', (_event: any, newNote: DbNote) => {
    const noteItem: Note = {
      id: newNote.id,
      title: newNote.title || newNote.content.substring(0, 20) + '...',
      content: newNote.content,
      originalContent: newNote.original_content,
      noteType: 'quick',
      time: dayjs(newNote.created_at).format('YYYY-MM-DD HH:mm'),
      createTime: newNote.created_at,
      updateTime: newNote.created_at
    }

    // 避免重复添加
    if (!notes.value.find(note => note.id === noteItem.id)) {
      notes.value = [noteItem, ...notes.value]

      // 如果当前选中的是这个笔记，更新选中的笔记
      if (selectedNoteId.value === noteItem.id) {
        selectedNote.value = noteItem;
      }
    }
  })

  // 监听本地笔记删除事件
  ipc.on('quick-note-deleted', (_event: any, id: number) => {
    notes.value = notes.value.filter(note => note.id !== id)
    if (selectedNoteId.value === id) {
      selectedNote.value = null;
      selectedNoteId.value = null;
    }
  })
})

onUnmounted(() => {
  // 清理主题相关监听器
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.removeEventListener('change', () => {})
  
  // 清理其他监听器
  ipc.removeAllListeners('quick-note-saved')
  ipc.removeAllListeners('quick-note-deleted')
  ipc.removeAllListeners('window-maximized-state')
})
</script>

<style scoped lang="scss">
.note-manager-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  border: 2px solid rgba(59, 130, 246, 0.15);
  border-radius: 16px;
  box-shadow: 
    0 25px 50px -12px rgba(59, 130, 246, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  background: #f1f5f9;
  position: relative;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 14px;
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  // 暗色主题样式 - 精美的深色设计
  html.dark &,
  :global(html.dark) & {
    background: #0f1419;
    border-color: rgba(59, 130, 246, 0.25);
    box-shadow: 
      0 25px 50px -12px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(59, 130, 246, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.03);

    &::before {
      background: rgba(59, 130, 246, 0.03);
    }
  }
}

.window-control-bar {
  background: #2563eb;
  padding: 12px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  -webkit-app-region: drag;
  user-select: none;
  min-height: 56px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 14px 14px 0 0;
  box-shadow: 
    0 1px 3px rgba(59, 130, 246, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: rgba(255, 255, 255, 0.2);
  }

  html.dark &,
  :global(html.dark) & {
    background: #1d4ed8;
    border-bottom-color: rgba(59, 130, 246, 0.2);
    box-shadow: 
      0 1px 3px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.08);

    &::after {
      background: rgba(59, 130, 246, 0.4);
    }
  }
}

.window-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.window-header-left {
  display: flex;
  align-items: center;
  gap: 10px;
}

.app-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.25);
  color: #ffffff;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 4px 12px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);

  &:hover {
    background: rgba(255, 255, 255, 0.35);
    transform: translateY(-1px);
    box-shadow: 
      0 6px 16px rgba(255, 255, 255, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  html.dark &,
  :global(html.dark) & {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 
      0 4px 12px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    &:hover {
      background: rgba(59, 130, 246, 0.3);
      box-shadow: 
        0 6px 16px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.15);
    }
  }
}

.window-title {
  margin: 0;
  font-size: 18px;
  font-weight: 800;
  color: #ffffff;
  letter-spacing: 0.5px;
  color: #ffffff;
  text-shadow: 
    0 2px 4px rgba(0, 0, 0, 0.2),
    0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  html.dark &,
  :global(html.dark) & {
    color: #f8fafc;
    text-shadow: 
      0 2px 4px rgba(0, 0, 0, 0.3),
      0 1px 2px rgba(0, 0, 0, 0.2);
  }
}

.window-header-actions {
  display: flex;
  gap: 6px;
  -webkit-app-region: no-drag;
}

.control-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 10px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  background: rgba(255, 255, 255, 0.15);
  color: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.12);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.2);
    transition: left 0.6s ease;
  }

  &:hover {
    background: 
      linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
    color: white;
    transform: translateY(-1px) scale(1.05);
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }

  &.theme-btn {
    position: relative;
    
    &:hover {
      background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
      border-color: rgba(139, 92, 246, 0.4);
      color: white;
      box-shadow: 
        0 4px 16px rgba(139, 92, 246, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }

    svg {
      transition: transform 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &:hover svg {
      transform: rotate(180deg) scale(1.1);
    }

    // 主题切换时的特殊动画
    &.theme-switching {
      animation: themeSwitch 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      
      svg {
        animation: shimmer 0.8s ease-in-out;
      }
    }
  }

  &.minimize-btn:hover {
    background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
    border-color: rgba(251, 191, 36, 0.4);
    color: white;
    box-shadow: 
      0 4px 16px rgba(251, 191, 36, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  &.maximize-btn:hover {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border-color: rgba(16, 185, 129, 0.4);
    color: white;
    box-shadow: 
      0 4px 16px rgba(16, 185, 129, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  &.close-btn:hover {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border-color: rgba(239, 68, 68, 0.4);
    color: white;
    box-shadow: 
      0 4px 16px rgba(239, 68, 68, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
    border-color: rgba(255, 255, 255, 0.08);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &:hover {
      background: 
        linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
      box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
  }
}

.main-content {
  display: flex;
  flex: 1;
  min-height: 0;
  background: transparent;
}

// 左侧笔记列表面板
.note-list-panel {
  width: 400px;
  min-width: 320px;
  max-width: 500px;
  display: flex;
  flex-direction: column;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(59, 130, 246, 0.12);
  overflow: hidden;
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.3),
    1px 0 20px rgba(59, 130, 246, 0.05);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, transparent 100%);
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(15, 23, 42, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%),
      linear-gradient(180deg, rgba(59, 130, 246, 0.03) 0%, transparent 100%);
    border-right-color: rgba(59, 130, 246, 0.2);
    box-shadow: 
      inset 0 1px 0 rgba(255, 255, 255, 0.08),
      1px 0 20px rgba(0, 0, 0, 0.3);

    &::before {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, transparent 100%);
    }
  }
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.05) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  gap: 8px;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.05) 100%);
    border-bottom-color: rgba(59, 130, 246, 0.2);
  }
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #9ca3af;
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 42px;
  padding: 0 42px 0 42px;
  border: 2px solid rgba(59, 130, 246, 0.15);
  border-radius: 14px;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  font-size: 15px;
  font-weight: 500;
  outline: none;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  color: #1e293b;
  backdrop-filter: blur(10px);

  &:focus {
    border-color: #3b82f6;
    background: 
      linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.9) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, transparent 100%);
    box-shadow: 
      0 0 0 4px rgba(59, 130, 246, 0.15),
      0 6px 20px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
  }

  &::placeholder {
    color: #94a3b8;
    font-weight: 400;
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(51, 65, 85, 0.9) 0%, rgba(30, 41, 59, 0.8) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
    border-color: rgba(59, 130, 246, 0.25);
    color: #f1f5f9;
    box-shadow: 
      0 4px 12px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &:focus {
      background: 
        linear-gradient(135deg, rgba(51, 65, 85, 0.95) 0%, rgba(30, 41, 59, 0.9) 100%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
      border-color: #3b82f6;
      box-shadow: 
        0 0 0 4px rgba(59, 130, 246, 0.2),
        0 6px 20px rgba(59, 130, 246, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    }

    &::placeholder {
      color: #64748b;
    }
  }
}

.clear-btn {
  position: absolute;
  right: 6px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  background: transparent;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #e5e7eb;
    color: #6b7280;
  }

  :global(.dark) & {
    color: #6b7280;

    &:hover {
      background: #4b5563;
      color: #9ca3af;
    }
  }
}

// 笔记列表
.note-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 3px;
    transition: all 0.2s ease;
    
    &:hover {
      background: rgba(59, 130, 246, 0.5);
    }
  }
}

.loading-container,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;

  :global(.dark) & {
    color: #9ca3af;
  }
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;

  :global(.dark) & {
    border-color: #4b5563;
    border-top-color: #3b82f6;
  }
}

.loading-text,
.empty-text {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
  letter-spacing: 0.3px;
}

.empty-action-btn {
  margin-top: 20px;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  gap: 10px;
  border: none;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  color: white;
  font-size: 17px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  letter-spacing: 0.3px;

  &:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
  }
}

.empty-icon {
  margin-bottom: 12px;
  color: #d1d5db;

  :global(.dark) & {
    color: #4b5563;
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.note-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.note-item {
  display: flex;
  padding: 16px;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.7) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  border: 2px solid rgba(59, 130, 246, 0.08);
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    transform: translateY(-2px);
    border-color: rgba(59, 130, 246, 0.2);
    box-shadow: 
      0 8px 25px rgba(59, 130, 246, 0.12),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    &::before {
      left: 100%;
    }
  }

  &.active {
    background: 
      linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, transparent 100%);
    border-color: #3b82f6;
    box-shadow: 
      0 12px 32px rgba(59, 130, 246, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);

    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 4px;
      background: linear-gradient(180deg, #3b82f6 0%, #2563eb 100%);
      border-radius: 0 2px 2px 0;
    }
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(30, 41, 59, 0.7) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
    border-color: rgba(59, 130, 246, 0.15);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &:hover {
      border-color: rgba(59, 130, 246, 0.3);
      box-shadow: 
        0 8px 25px rgba(0, 0, 0, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    }

    &.active {
      background: 
        linear-gradient(135deg, rgba(59, 130, 246, 0.25) 0%, rgba(147, 197, 253, 0.15) 100%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
      border-color: #3b82f6;
      box-shadow: 
        0 12px 32px rgba(59, 130, 246, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
    }
  }
}

.note-content {
  flex: 1;
  min-width: 0;
}

.note-title {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 700;
  color: #1e293b;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  letter-spacing: 0.3px;

  :global(.dark) & {
    color: #f8fafc;
  }
}

.note-preview {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #64748b;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-weight: 400;

  :global(.dark) & {
    color: #94a3b8;
  }
}

.note-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 13px;
  color: #9ca3af;

  :global(.dark) & {
    color: #6b7280;
  }
}

.note-time {
  font-weight: 400;
}

.note-type {
  padding: 3px 10px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  border: 1px solid rgba(59, 130, 246, 0.2);
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  color: #3b82f6;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.1) 100%);
    border-color: rgba(59, 130, 246, 0.3);
    color: #93c5fd;
  }
}

.note-actions {
  display: flex;
  align-items: flex-start;
  margin-left: 12px;
}

.delete-btn {
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: #9ca3af;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0;

  .note-item:hover & {
    opacity: 1;
  }

  &:hover {
    background: #fee2e2;
    color: #dc2626;
  }

  :global(.dark) & {
    color: #6b7280;

    &:hover {
      background: #7f1d1d;
      color: #f87171;
    }
  }
}

// 右侧内容面板
.note-content-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.85) 0%, rgba(248, 250, 252, 0.75) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  backdrop-filter: blur(20px);
  box-shadow: 
    inset 0 1px 0 rgba(255, 255, 255, 0.25),
    -1px 0 20px rgba(59, 130, 246, 0.03);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.01) 0%, transparent 100%);
    z-index: 0;
  }

  > * {
    position: relative;
    z-index: 1;
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(15, 23, 42, 0.9) 0%, rgba(30, 41, 59, 0.85) 100%),
      linear-gradient(180deg, rgba(59, 130, 246, 0.02) 0%, transparent 100%);
    box-shadow: 
      inset 0 1px 0 rgba(255, 255, 255, 0.06),
      -1px 0 20px rgba(0, 0, 0, 0.2);

    &::before {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, transparent 100%);
    }
  }
}

.editor-panel,
.preview-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  overflow: hidden;
}

.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;

  :global(.dark) & {
    color: #9ca3af;
  }
}

.select-icon {
  margin-bottom: 16px;
  color: #d1d5db;

  :global(.dark) & {
    color: #4b5563;
  }
}

.select-text {
  margin: 0;
  font-size: 18px;
}

.note-detail {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.detail-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 24px 30px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.03) 0%, rgba(147, 197, 253, 0.03) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(147, 197, 253, 0.03) 100%);
    border-bottom-color: rgba(59, 130, 246, 0.2);
  }
}

.detail-title {
  margin: 0;
  font-size: 36px;
  font-weight: 900;
  color: #1e293b;
  line-height: 1.2;
  flex: 1;
  min-width: 0;
  letter-spacing: -0.5px;
  background: 
    linear-gradient(135deg, #1e293b 0%, #475569 50%, #64748b 100%),
    linear-gradient(180deg, #1e293b 0%, #334155 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(30, 41, 59, 0.1);
  transition: all 0.3s ease;

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%),
      linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

.detail-actions {
  display: flex;
  gap: 12px;
  margin-left: 20px;
}

.detail-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 14px;
  background: 
    linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(248, 250, 252, 0.6) 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid rgba(59, 130, 246, 0.1);
  backdrop-filter: blur(10px);
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    background: 
      linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 197, 253, 0.1) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    color: #3b82f6;
    border-color: rgba(59, 130, 246, 0.3);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 
      0 8px 25px rgba(59, 130, 246, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    &::before {
      left: 100%;
    }
  }

  &.danger {
    &:hover {
      background: 
        linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(248, 113, 113, 0.1) 100%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
      color: #ef4444;
      border-color: rgba(239, 68, 68, 0.3);
      box-shadow: 
        0 8px 25px rgba(239, 68, 68, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
  }

  html.dark &,
  :global(html.dark) & {
    background: 
      linear-gradient(135deg, rgba(51, 65, 85, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.03) 0%, transparent 100%);
    color: #64748b;
    border-color: rgba(59, 130, 246, 0.15);
    box-shadow: 
      0 2px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);

    &:hover {
      background: 
        linear-gradient(135deg, rgba(59, 130, 246, 0.25) 0%, rgba(147, 197, 253, 0.15) 100%),
        linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
      color: #60a5fa;
      border-color: rgba(59, 130, 246, 0.4);
      box-shadow: 
        0 8px 25px rgba(59, 130, 246, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.08);
    }

    &.danger {
      &:hover {
        background: 
          linear-gradient(135deg, rgba(239, 68, 68, 0.25) 0%, rgba(248, 113, 113, 0.15) 100%),
          linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
        color: #f87171;
        border-color: rgba(239, 68, 68, 0.4);
        box-shadow: 
          0 8px 25px rgba(239, 68, 68, 0.25),
          inset 0 1px 0 rgba(255, 255, 255, 0.08);
      }
    }
  }
}

.detail-meta {
  padding: 16px 30px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.85) 0%, rgba(241, 245, 249, 0.85) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
  flex-shrink: 0;

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.85) 0%, rgba(51, 65, 85, 0.85) 100%);
    border-bottom-color: rgba(59, 130, 246, 0.2);
  }
}

.meta-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  font-size: 14px;
  padding: 4px 0;

  &:last-child {
    margin-bottom: 0;
  }
}

.meta-label {
  min-width: 90px;
  color: #64748b;
  font-weight: 600;
  letter-spacing: 0.3px;
  padding-top: 2px;

  :global(.dark) & {
    color: #94a3b8;
  }
}

.meta-value {
  color: #1e293b;
  font-weight: 500;

  :global(.dark) & {
    color: #f1f5f9;
  }
}

.tag-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  padding: 4px 10px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 197, 253, 0.1) 100%);
  color: #1d4ed8;
  border: 1px solid transparent;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 600;
  letter-spacing: 0.2px;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.2) 100%);
  }

  :global(.dark) & {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(147, 197, 253, 0.1) 100%);
    color: #93c5fd;
    border-color: rgba(59, 130, 246, 0.3);

    &:hover {
      background: linear-gradient(135deg, rgba(59, 130, 246, 0.3) 0%, rgba(147, 197, 253, 0.2) 100%);
      border-color: rgba(59, 130, 246, 0.4);
    }
  }
}

.detail-content {
  flex: 1;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(59, 130, 246, 0.6);
    }
  }
}

.detail-content-wrapper {
  padding: 30px;
}

.content-text {
  font-size: 17px;
  line-height: 1.9;
  color: #334155;
  white-space: pre-wrap;
  word-break: break-word;
  font-weight: 400;
  letter-spacing: 0.3px;

  :global(.dark) & {
    color: #e2e8f0;
  }
}

.detail-summary {
  padding: 24px 30px;
  border-top: 1px solid rgba(59, 130, 246, 0.1);
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  flex-shrink: 0;

  :global(.dark) & {
    border-top-color: rgba(59, 130, 246, 0.2);
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }
}

.summary-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1e3a8a;
  letter-spacing: 0.3px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.action-btn.primary {
  width: 42px;
  height: 42px;
  border-radius: 14px;
  border: none;
  background: 
    linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%),
    linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, transparent 100%);
  color: white;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 
    0 6px 20px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
  }

  &:hover {
    background: 
      linear-gradient(135deg, #2563eb 0%, #1d4ed8 50%, #1e40af 100%),
      linear-gradient(180deg, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 
      0 10px 30px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);

    &::before {
      left: 100%;
    }
  }

  &:active {
    transform: translateY(0) scale(0.98);
  }

  html.dark &,
  :global(html.dark) & {
    box-shadow: 
      0 6px 20px rgba(59, 130, 246, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);

    &:hover {
      box-shadow: 
        0 10px 30px rgba(59, 130, 246, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
  }
}

// 主题切换动画
@keyframes themeSwitch {
  0% {
    transform: scale(1) rotate(0deg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  25% {
    transform: scale(1.1) rotate(90deg);
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  }
  50% {
    transform: scale(1.2) rotate(180deg);
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 12px 35px rgba(139, 92, 246, 0.6);
  }
  75% {
    transform: scale(1.1) rotate(270deg);
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
  }
  100% {
    transform: scale(1) rotate(360deg);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

// 整体主题切换过渡
.note-manager-container {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  
  &.theme-switching {
    transition: all 0.15s ease-out;
  }

  * {
    transition: 
      background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
      box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}

// 确保深色主题样式的优先级
html.dark,
:global(html.dark) {
  .note-manager-container {
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }
}
</style>