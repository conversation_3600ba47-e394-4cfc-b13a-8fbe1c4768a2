"use strict";

const { Service } = require("ee-core");
const appjson = require("../../database/appjson");
const Ps = require("ee-core/ps");

/**
 * json数据存储
 * @class
 */
class JsondbService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  /**
   * 创建初始数据
   */
  createInitData() {
    // 检查并设置应用数据
    if (!appjson.getAppData()) {
      appjson.setAppData({
        url: "http://10.11.24.110",
        version: "3.0.1",
      });
    }

    // 检查并设置系统配置
    if (!appjson.getSystemConfig()) {
      appjson.setSystemConfig({
        fileSavePath: (Ps.getUserHomeDir() + "/Downloads").replace(/\//g, "\\"),
        hongyan: {
          url: "",
          agentName: "",
          esensign: "",
          esentoken: "",
        },
        bailing: {
          url: "http://10.11.24.129:8088/bailing/chatRpc/streamChatRpc",
        },
      });
    }
  }

  /**
   * 用户缓存相关
   */
  setItem(name, value) {
    appjson.appInfoDB.setItem(name, value);
  }
  getItem(name) {
    return appjson.appInfoDB.getItem(name);
  }
  removeItem(name) {
    appjson.appInfoDB.setItem(name, null);
  }
  async getUserId() {
    let userInfo = await this.getItem("userInfo");
    if (!userInfo) return "";

    userInfo = JSON.parse(userInfo);
    return userInfo.id;
  }

  /**
   * 系统配置相关
   */
  saveConfigField(configField = {}) {
    appjson.setSystemConfig(configField);
  }
  getAllConfigField() {
    return appjson.getSystemConfig();
  }
}

JsondbService.toString = () => "[class JsondbService]";
module.exports = JsondbService;
