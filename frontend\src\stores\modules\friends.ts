import { defineStore } from "pinia";
import piniaPersistConfig from "../helper/persist";
import * as contactApi from "@/api/modules/contact";
import { useContactsStore } from "./contacts";

interface Friend {
  groupTree: Array<any>;
  friendList: Array<any>;
}

export const useFriendStore = defineStore("lark-friends", {
  state: (): Friend => ({
    groupTree: [],
    friendList: []
  }),
  getters: {},
  actions: {
    // 获取好友分组
    async getFriendGroup(params: any) {
      const { data } = await contactApi.getFriendGroup(params);
      this.groupTree = [{ id: "default", groupName: "默认分组" }].concat(data);
    },
    // 增加好友分组
    async addFriendGroup(params: any) {
      this.groupTree.push(params);
    },
    // 删除好友分组
    deleteFriendGroup(id: string) {
      const index = this.groupTree.findIndex((item: any) => item.id == id);
      this.groupTree.splice(index, 1);
    },
    // 修改好友分组
    updateFriendGroup(id: string, name: string) {
      const index = this.groupTree.findIndex((item: any) => item.id == id);
      this.groupTree[index].groupName = name;
    },
    // 获取全部好友
    async getAllFriend(params: any) {
      const { data } = await contactApi.getAllFriend(params);
      const contactsStore = useContactsStore();
      for (let item of data) {
        let fileds: any = ["avatar", 'isOnline']
        if (item.online === null) fileds = ["avatar"]
        // 更新用户状态
        contactsStore.updateContact(
          {
            id: item.id,
            name: item.name,
            avatar: item.avatar,
            secretLevel: item.secretLevel,
            isOnline: item.online == "on"
          },
          fileds
        );
      }
      
      contactsStore.updateSqliteData();
      this.friendList = data;
    },
    // 删除好友
    deleteFriend(friendShipId: string) {
      const index = this.friendList.findIndex((item: any) => item.friendShipId == friendShipId);
      this.friendList.splice(index, 1);
    },
    // 修改好友
    updateFriend(type: number, friendShipId: string, groupId?: string) {
      const index = this.friendList.findIndex((item: any) => item.friendShipId == friendShipId);
      if (type == 1) {
        this.friendList[index].groupId = groupId;
        this.friendList[index].status = "1";
      } else if (type == 2) {
        this.friendList[index].status = "2";
      } else {
        this.friendList[index].groupId = groupId;
      }
    }
  },
  persist: piniaPersistConfig("lark-friend")
});
