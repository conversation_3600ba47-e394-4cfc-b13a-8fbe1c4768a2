<template>
  <WinHeader target-win="config" ref="winHeader" />

  <el-form :model="formData" :rules="rules" ref="ruleForm" class="w-full h-[100%] user-select-none">
    <el-row class="h-[100%] text-sm">
      <!-- 左侧分类 -->
      <el-col :span="4">
        <div class="menu-container border-r border-gray-300">
          <ul class="list-none p-0 m-0">
            <li :class="{ active: activeTab === '1' }" @click="onSelect('1')"><span>窗口设置</span></li>
            <li :class="{ active: activeTab === '2' }" @click="onSelect('2')"><span>聊天设置</span></li>
            <li :class="{ active: activeTab === '3' }" @click="onSelect('3')"><span>调试设置</span></li>
            <li :class="{ active: activeTab === '4' }" @click="onSelect('4')"><span>大模型设置</span></li>
            <li :class="{ active: activeTab === '5' }" @click="onSelect('5')"><span>更新设置</span></li>
          </ul>
        </div>
      </el-col>

      <!-- 右侧内容 -->
      <el-col :span="20" class="right-container h-[100%]">
        <div v-if="activeTab === '1'">
          <div class="flex justify-between space-x-4 pt-4">
            <el-form-item label="窗口宽度" prop="windowSize.width">
              <el-input v-model.number="formData.windowSize.width" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="窗口高度" prop="windowSize.height">
              <el-input v-model.number="formData.windowSize.height" placeholder="请输入"></el-input>
            </el-form-item>
          </div>
          <div class="flex justify-between space-x-4">
            <el-form-item label="最小宽度" prop="windowSize.minWidth">
              <el-input v-model.number="formData.windowSize.minWidth" placeholder="请输入"></el-input>
            </el-form-item>
            <el-form-item label="最小高度" prop="windowSize.minHeight">
              <el-input v-model.number="formData.windowSize.minHeight" placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </div>

        <div v-if="activeTab === '2'" class="divider">
          <div class="flex w-full items-center">
            <div class="flex-1 mr-4 text-sm save-label">
              <div>文件保存地址</div>
              <div>{{ formData.fileSavePath }}</div>
            </div>
            <el-button type="default" @click="changePath">更改</el-button>
          </div>
          <ToggleItem label="启用聊天机器人" v-model="formData.chatbotAvailable"></ToggleItem>
          <ToggleItem label="启用新消息通知" v-model="formData.msgNotifyEnable"></ToggleItem>
        </div>

        <div v-if="activeTab === '3'" class="divider">
          <ToggleItem label="启用调试工具" v-model="formData.openDevTools"></ToggleItem>
          <div>
            <ToggleItem label="启用远程" v-model="formData.remoteUrl.enable"></ToggleItem>
            <el-form-item
              label="远程地址"
              prop="remoteUrl.url"
              :rules="{ required: formData.remoteUrl.enable, message: '请输入远程地址', trigger: 'blur' }"
              class="mt-[8px]"
            >
              <el-input v-model="formData.remoteUrl.url" placeholder="请输入"></el-input>
            </el-form-item>
          </div>
        </div>

        <el-scrollbar v-if="activeTab === '4'" class="h-[100%]">
          <div class="title-with-separator">
            <span>鸿雁</span>
          </div>
          <el-form-item label="url" prop="hongyan.url">
            <el-input v-model="formData.hongyan.url" placeholder="请输入" type="textarea" :rows="2" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="agentName" prop="hongyan.agentName">
            <el-input
              v-model="formData.hongyan.agentName"
              placeholder="请输入"
              type="textarea"
              :rows="2"
              resize="none"
            ></el-input>
          </el-form-item>
          <el-form-item label="esensign" prop="hongyan.esensign">
            <el-input v-model="formData.hongyan.esensign" placeholder="请输入" type="textarea" :rows="3" resize="none"></el-input>
          </el-form-item>
          <el-form-item label="esentoken" prop="hongyan.esentoken">
            <el-input
              v-model="formData.hongyan.esentoken"
              placeholder="请输入"
              type="textarea"
              :rows="3"
              resize="none"
            ></el-input>
          </el-form-item>

          <div class="title-with-separator">
            <span>百灵</span>
          </div>
          <el-form-item label="url" prop="bailing.url">
            <el-input v-model="formData.bailing.url" placeholder="请输入" type="textarea" :rows="2" resize="none"></el-input>
          </el-form-item>

          <div style="height: 40vh"></div>
        </el-scrollbar>

        <div v-if="activeTab === '5'" class="divider">
          <ToggleItem label="启用自动更新" v-model="formData.autoUpdateEnable"></ToggleItem>
          <div class="flex justify-between hover:bg-[#ededed]">
            <span>获取云雀最新版本</span>
            <font-awesome-icon :icon="['fas', 'external-link']" />
          </div>
        </div>
      </el-col>
    </el-row>
  </el-form>

  <div class="absolute bottom-0 z-20 w-full flex justify-center bg-white py-5">
    <el-button type="danger" icon="delete" class="mr-8" @click="clearChatSettings">清空缓存</el-button>
    <el-button type="default" @click="cancel">取消</el-button>
    <el-button type="primary" @click="save">保存设置</el-button>
  </div>
</template>

<script setup lang="ts">
import WinHeader from "@/layouts/components/WinHeader.vue";
import ToggleItem from "./components/ToggleItem.vue";
import { ElMessageBox } from "element-plus";
import { ref, reactive, onBeforeMount } from "vue";
import { ipc } from "@/utils/ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";

const ruleForm: any = ref(null);
const winHeader: any = ref(null);

const formData = reactive({
  windowSize: {
    width: 1250,
    height: 800,
    minWidth: 1250,
    minHeight: 800
  },
  remoteUrl: {
    enable: false,
    url: ""
  },
  fileSavePath: "",
  chatbotAvailable: true,
  openDevTools: false,
  msgNotifyEnable: true,
  hongyan: {
    url: "",
    agentName: "",
    esensign: "",
    esentoken: ""
  },
  bailing: {
    url: ""
  },
  autoUpdateEnable: true
});

// 获取配置数据
const getConfigData = async () => {
  const config = await ipc.invoke(ipcApiRoute.getConfig);
  if (config) {
    Object.assign(formData, config);
  }
};

// 验证远程URL
const validateRemoteUrl = (rule, value, callback) => {
  if (!formData.remoteUrl.enable || value) {
    return callback();
  }
  callback(new Error("请输入远程地址"));
};

const rules = {
  windowSize: {
    width: [{ required: true, message: "请输入窗口宽度", trigger: "blur" }],
    height: [{ required: true, message: "请输入窗口高度", trigger: "blur" }],
    minWidth: [{ required: true, message: "请输入最小宽度", trigger: "blur" }],
    minHeight: [{ required: true, message: "请输入最小高度", trigger: "blur" }]
  },
  remoteUrl: {
    url: [{ validator: validateRemoteUrl, trigger: "blur" }]
  },
  hongyan: {
    url: [{ required: true, message: "请输入 url", trigger: "blur" }],
    agentName: [{ required: true, message: "请输入 agentName", trigger: "blur" }],
    esensign: [{ required: true, message: "请输入 esensign", trigger: "blur" }],
    esentoken: [{ required: true, message: "请输入 esentoken", trigger: "blur" }]
  },
  bailing: {
    url: [{ required: true, message: "请输入 url", trigger: "blur" }]
  }
};

// 更改保存路径
const changePath = async () => {
  const path = await ipc.invoke(ipcApiRoute.selectFolder);
  if (path) formData.fileSavePath = path;
};

// 切换标签页前验证当前表单
const activeTab = ref("1");
const onSelect = (tab: string) => {
  if (activeTab.value === tab) return;

  // 校验当前表单
  ruleForm.value.validate((valid: any) => {
    if (!valid) {
      return;
    }
    activeTab.value = tab;
  });
};

// 清空缓存
const clearChatSettings = () => {
  ElMessageBox.confirm("确定要清空聊天记录和缓存吗？", "确认操作", {
    confirmButtonText: "清空并关闭",
    cancelButtonText: "取消",
    type: "warning",
    customClass: "config-message-box"
  })
    .then(() => {
      ipc.send(ipcApiRoute.clearLocalCache);
      winHeader.value.closeWindow();
    })
    .catch(() => {});
};

const cancel = () => {
  winHeader.value.closeWindow();
};

// 保存配置数据
const save = () => {
  ruleForm.value.validate(async (valid: any) => {
    if (valid) {
      const configData = JSON.parse(JSON.stringify(formData));
      if (!configData.remoteUrl.enable) {
        configData.remoteUrl.url = "";
      }
      await ipc.invoke(ipcApiRoute.setConfig, configData);

      ElMessageBox.confirm("设置已成功保存，是否重启以应用更改？", "提示", {
        confirmButtonText: "重启",
        cancelButtonText: "取消",
        type: "warning",
        customClass: "config-message-box"
      })
        .then(() => {
          ipc.send(ipcApiRoute.relaunch);
        })
        .catch(() => {
          winHeader.value.closeWindow();
        });
    }
  });
};

onBeforeMount(() => {
  getConfigData();
});
</script>

<style lang="scss" scoped>
.right-container > div {
  padding-left: 10px;
  padding-right: 10px;

  > div {
    padding-left: 6px;
    padding-right: 6px;
  }
}

.save-label {
  @apply relative;
  &::after {
    content: "";
    @apply absolute top-[15%] right-0 w-[1px] h-[70%] bg-[--el-border-color];
  }
}

.menu-container li {
  @apply flex items-center justify-center;
  @apply text-black bg-white cursor-pointer py-1 h-9;
  &.active {
    color: #409eff;
  }
}

.divider > div {
  border-top: 1px solid var(--el-border-color);
  padding: 12px 0;
}

.divider > div:first-of-type {
  border-top: none;
}

.title-with-separator {
  position: relative;
  margin: 16px 0 12px;
  text-align: center;
  font-size: 14px;

  &::before {
    content: "";
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: #ccc;
    transform: translateY(-50%) scaleY(0.5);
    z-index: 1;
  }

  span {
    padding: 0 24px;
    position: relative;
    z-index: 2;
    background-color: #fff;
    display: inline-block;
  }
}
</style>
<style>
.config-message-box {
  width: 380px;
}
</style>
