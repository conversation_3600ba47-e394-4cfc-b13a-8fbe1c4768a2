'use strict'

const { Controller } = require('ee-core')
const { BrowserWindow, screen } = require('electron')
const path = require('path')
const Addon = require('ee-core/addon')
const Ps = require('ee-core/ps')
const Conf = require('ee-core/config')

// 闪记窗口实例
let quickNoteWin = null
// 笔记管理窗口实例
let noteManagerWin = null
// 笔记管理窗口是否最大化
let isNoteManagerMaximized = false
// 存储窗口最大化前的位置和大小
let noteManagerWinBounds = null

class NoteController extends Controller {
  constructor(ctx) {
    super(ctx)
    console.log('NoteController loaded successfully')
    // 初始化笔记服务
    this.initNoteService()
  }

  /**
   * 初始化笔记服务
   */
  initNoteService() {
    try {
      // 使用SQLite数据库笔记服务
      const NoteService = require('../service/note')
      this.noteService = new NoteService(this.ctx)
      console.log('SQLite笔记服务初始化成功')
    } catch (error) {
      console.error('SQLite笔记服务初始化失败:', error)
      throw new Error('笔记服务初始化失败，请检查数据库配置')
    }
  }

  /**
   * 获取笔记服务实例
   */
  getNoteService() {
    if (!this.noteService) {
      throw new Error('笔记服务未初始化，请检查数据库连接')
    }
    return this.noteService
  }

  /**
   * 打开闪记编辑器窗口
   * @param {Object} noteData - 可选的笔记数据，用于编辑模式
   */
  openQuickNoteWindow(noteData) {
    console.log('[Main] openQuickNoteWindow 被调用，数据:', noteData)
    
    // 检查窗口是否已经存在
    if (quickNoteWin) {
      console.log('[Main] 窗口已存在，发送数据到现有窗口');
      quickNoteWin.focus()
      // 如果是编辑模式，发送笔记数据
      if (noteData) {
        console.log('[Main] 准备发送数据到现有窗口');
        quickNoteWin.webContents.send('edit-note-data', noteData)
        console.log('[Main] 数据已发送到现有窗口');
      }
      return 'Window already exists'
    }

    console.log('[Main] 创建新窗口');
    const windowTitle = "闪记"
    const windowName = "闪记"
    const content = "#/quick-note-editor"
    
    // 构建完整的 URL
    let contentUrl = null
    let addr = "http://localhost:8080/"
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr = mainServer.protocol + path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }
    contentUrl = addr + content
    console.log('[Main] 目标URL:', contentUrl);

    const options = {
      title: windowTitle,
      width: 640,
      height: 720,
      frame: false,
      resizable: false,
      show: true,
      maximizable: false,
      minimizable: true,
      fullscreenable: false,
      acceptFirstMouse: true,
      transparent: true,  // 与主窗口保持一致
      alwaysOnTop: false,
      navigateOnDragDrop: true,  // 添加主窗口的配置
      movable: true,             // 添加主窗口的配置
      closable: true,            // 添加主窗口的配置
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        devTools: false,
      }
    }

    // 使用 ee-core 的 Addon 创建窗口
    console.log('[Main] 开始创建窗口');
    quickNoteWin = Addon.get("window").create(windowName, options)
    
    // 设置窗口位置（居中显示）
    const [screenWidth, screenHeight] = [
      screen.getPrimaryDisplay().workAreaSize.width,
      screen.getPrimaryDisplay().workAreaSize.height,
    ]
    quickNoteWin.setPosition(
      Math.floor((screenWidth - 640) / 2),
      Math.floor((screenHeight - 720) / 2)
    )

    // 加载页面
    console.log('[Main] 开始加载页面');
    quickNoteWin.loadURL(contentUrl)

    // 监听页面加载错误
    quickNoteWin.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      console.error('[Main] 页面加载失败:', errorCode, errorDescription);
    });

    // 等待页面完全加载后再发送数据
    quickNoteWin.webContents.on('did-finish-load', () => {
      console.log('[Main] 页面加载完成');
      if (noteData) {
        console.log('[Main] 准备发送数据到新窗口');
        // 添加延迟，确保渲染进程已经准备好
        setTimeout(() => {
          console.log('[Main] 发送数据到新窗口:', noteData);
          quickNoteWin.webContents.send('edit-note-data', noteData);
          console.log('[Main] 数据已发送到新窗口');
        }, 500); // 延迟500ms
      }
    });
    
    quickNoteWin.on("ready-to-show", () => {
      console.log('[Main] 窗口准备显示');
      // 添加延迟确保样式完全应用
      setTimeout(() => {
        quickNoteWin.show()
        // 自动打开开发者工具
        quickNoteWin.webContents.openDevTools()
      }, 100)
    })

    quickNoteWin.on("close", () => {
      console.log('[Main] 窗口关闭');
      quickNoteWin = null
    })
    
    return 'Window created successfully'
  }

  /**
   * 关闭闪记编辑器窗口
   */
  closeQuickNoteWindow() {
    if (quickNoteWin) {
      quickNoteWin.close()
      quickNoteWin = null
    }
    return 'Window closed'
  }

  /**
   * 最小化闪记编辑器窗口
   */
  minimizeQuickNoteWindow() {
    if (quickNoteWin) {
      quickNoteWin.minimize()
    }
    return 'Window minimized'
  }

  /**
   * 移动窗口
   */
  moveWindow({ deltaX, deltaY }) {
    if (quickNoteWin) {
      const [currentX, currentY] = quickNoteWin.getPosition()
      const newX = currentX + deltaX
      const newY = currentY + deltaY
      
      // 获取屏幕尺寸以确保窗口不会超出屏幕边界
      const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize
      const [winWidth, winHeight] = quickNoteWin.getSize()
      
      // 限制窗口位置在屏幕范围内
      const boundedX = Math.max(0, Math.min(newX, screenWidth - winWidth))
      const boundedY = Math.max(0, Math.min(newY, screenHeight - winHeight))
      
      quickNoteWin.setPosition(boundedX, boundedY)
    }
    return 'Window moved'
  }

  /**
   * 保存笔记
   * @param {object} noteData - 笔记数据对象
   */
  async saveNote(noteData) {
    try {
      console.log('收到保存笔记请求:', noteData)
      
      // 确保noteData是一个对象
      if (typeof noteData !== 'object') {
        noteData = {
          content: noteData,
          title: noteData.substring(0, 20) + (noteData.length > 20 ? '...' : '')
        }
      }

      // 验证必需字段
      if (!noteData.content || noteData.content.trim() === '') {
        throw new Error('笔记内容不能为空')
      }

      // 使用数据库服务保存笔记
      const noteService = this.getNoteService()
      if (!noteService) {
        throw new Error('笔记服务未初始化')
      }

      // 提取必要的字段
      const { content, title, id, originalContent } = noteData
      console.log('准备保存笔记到数据库:', { content: content.substring(0, 50) + '...', title, id, hasOriginalContent: !!originalContent })
      
      const result = await noteService.save(content, title, id, originalContent)
      
      console.log('笔记保存成功:', result)

      // 获取所有窗口并通知更新
      const allWindows = BrowserWindow.getAllWindows()
      allWindows.forEach(win => {
        if (!win.isDestroyed()) {
          try {
            win.webContents.send('quick-note-saved', result)
          } catch (err) {
            console.warn('向窗口发送更新通知失败:', err)
          }
        }
      })

      return result
    } catch (error) {
      console.error('保存笔记失败:', error)
      throw error
    }
  }

  /**
   * 获取所有笔记
   */
  async getAllNotes() {
    try {
      const noteService = this.getNoteService()
      if (!noteService) {
        console.warn('笔记服务未初始化，返回空数组')
        return []
      }
      const notes = await noteService.getAll()
      console.log('获取笔记列表成功:', notes.length, '条')
      return notes
    } catch (error) {
      console.error('获取笔记列表失败:', error)
      return []
    }
  }

  /**
   * 删除笔记
   * @param {number} id - 笔记ID
   * @param {boolean} hardDelete - 是否硬删除
   */
  async deleteNote(id, hardDelete = false) {
    try {
      const noteService = this.getNoteService()
      if (!noteService) {
        throw new Error('笔记服务未初始化')
      }
      const result = await noteService.delete(id, hardDelete)
      
      console.log('笔记删除成功:', id)
      
      // 通知主窗口刷新
      const mainWindow = BrowserWindow.getAllWindows().find(win => win.getTitle() !== '新建闪记')
      if (mainWindow) {
        mainWindow.webContents.send('quick-note-deleted', id)
      }
      
      return { success: true, result }
    } catch (error) {
      console.error('删除笔记失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取笔记
   * @param {number} id - 笔记ID
   */
  async getNoteById(id) {
    try {
      const noteService = this.getNoteService()
      if (!noteService) {
        throw new Error('笔记服务未初始化')
      }
      const note = await noteService.getById(id)
      console.log('获取笔记成功:', id)
      return note
    } catch (error) {
      console.error('获取笔记失败:', error)
      throw error
    }
  }

  /**
   * 搜索笔记
   * @param {string} keyword - 搜索关键词
   * @param {object} options - 搜索选项
   */
  async searchNotes(keyword, options = {}) {
    try {
      const noteService = this.getNoteService()
      if (!noteService) {
        console.warn('笔记服务未初始化，返回空数组')
        return []
      }
      const notes = await noteService.search(keyword, options)
      console.log('搜索笔记成功:', notes.length, '条')
      return notes
    } catch (error) {
      console.error('搜索笔记失败:', error)
      throw error
    }
  }

  /**
   * 获取笔记统计信息
   * @param {string} userId - 用户ID
   */
  async getNoteStats(userId) {
    try {
      const noteService = this.getNoteService()
      if (!noteService) {
        console.warn('笔记服务未初始化，返回默认统计')
        return { totalCount: 0, todayCount: 0 }
      }
      const stats = await noteService.getStats(userId)
      console.log('获取笔记统计成功:', stats)
      return stats
    } catch (error) {
      console.error('获取笔记统计失败:', error)
      throw error
    }
  }

  /**
   * 打开笔记管理窗口
   */
  openNoteManagerWindow() {
    // 检查窗口是否已经存在
    if (noteManagerWin) {
      noteManagerWin.focus()
      return 'Window already exists'
    }

    const windowTitle = "笔记管理"
    const windowName = "NoteManager"
    const content = "#/note-manager"
    
    // 构建完整的 URL
    let contentUrl = null
    let addr = "http://localhost:8080/"
    if (Ps.isProd()) {
      const mainServer = Conf.getValue("mainServer");
      if (Conf.isFileProtocol(mainServer)) {
        addr = mainServer.protocol + path.join(Ps.getHomeDir(), mainServer.indexPath);
      } else {
        addr = mainServer.protocol + mainServer.host + ":" + mainServer.port;
      }
    }
    // 在生产环境中，这里应该使用正确的文件路径
    contentUrl = addr + content

    const options = {
      title: windowTitle,
      width: 1200,
      height: 800,
      frame: false,
      resizable: true,
      show: false,
      maximizable: true,
      minimizable: true,
      fullscreenable: true,
      acceptFirstMouse: true,
      transparent: true,  // 恢复透明，与主窗口保持一致
      hasShadow: false,   // 保持无阴影
      alwaysOnTop: false,
      navigateOnDragDrop: true,  // 添加主窗口的配置
      movable: true,             // 添加主窗口的配置
      closable: true,            // 添加主窗口的配置
      center: true,              // 添加主窗口的配置
      webPreferences: {
        nodeIntegration: true,
        contextIsolation: false,
        devTools: false,
      },
      // 移除可能导致圆角的Windows特定设置
      // backgroundColor: '#ffffff',
      // roundedCorners: false,
      // titleBarStyle: 'hidden',
      // thickFrame: false,
      // vibrancy: 'under-window',
      // visualEffectState: 'active'
    }

    // 使用 ee-core 的 Addon 创建窗口
    noteManagerWin = Addon.get("window").create(windowName, options)
    
    noteManagerWin.on('maximize', () => {
      isNoteManagerMaximized = true;
      if (noteManagerWin && !noteManagerWin.isDestroyed()) {
        noteManagerWin.webContents.send('window-maximized-state', true);
      }
    });

    noteManagerWin.on('unmaximize', () => {
      isNoteManagerMaximized = false;
      if (noteManagerWin && !noteManagerWin.isDestroyed()) {
        noteManagerWin.webContents.send('window-maximized-state', false);
      }
    });

    // 设置窗口位置（居中显示）
    const [screenWidth, screenHeight] = [
      screen.getPrimaryDisplay().workAreaSize.width,
      screen.getPrimaryDisplay().workAreaSize.height,
    ]
    noteManagerWin.setPosition(
      Math.floor((screenWidth - 1200) / 2),
      Math.floor((screenHeight - 800) / 2)
    )

    // 加载页面
    noteManagerWin.loadURL(contentUrl)
    
    // 使用与主窗口相同的显示逻辑
    noteManagerWin.once("ready-to-show", () => {
      noteManagerWin.show()
      noteManagerWin.focus()
    })

    noteManagerWin.on("close", () => {
      noteManagerWin = null
      isNoteManagerMaximized = false
      noteManagerWinBounds = null
    })
    
    return 'Window created successfully'
  }

  /**
   * 关闭笔记管理窗口
   */
  closeNoteManagerWindow() {
    if (noteManagerWin) {
      noteManagerWin.close()
      noteManagerWin = null
    }
    return 'Window closed'
  }

  /**
   * 最小化笔记管理窗口
   */
  minimizeNoteManagerWindow() {
    if (noteManagerWin) {
      noteManagerWin.minimize()
    }
    return 'Window minimized'
  }

  /**
   * 最大化/还原笔记管理窗口
   */
  toggleMaximizeNoteManagerWindow() {
    if (!noteManagerWin) {
      return 'Window not found';
    }
    
    if (noteManagerWin.isDestroyed()) {
      return 'Window is destroyed';
    }
    
    // 获取屏幕工作区域尺寸
    const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize;
    const currentBounds = noteManagerWin.getBounds();
    
    // 判断当前窗口是否"手动最大化"（几乎占满屏幕）
    const isManuallyMaximized = (
      currentBounds.width >= screenWidth - 20 && 
      currentBounds.height >= screenHeight - 20 &&
      currentBounds.x <= 10 &&
      currentBounds.y <= 10
    );
    
    if (isManuallyMaximized || isNoteManagerMaximized) {
      // 还原窗口
      const restoreWidth = 1200;
      const restoreHeight = 800;
      const restoreX = Math.floor((screenWidth - restoreWidth) / 2);
      const restoreY = Math.floor((screenHeight - restoreHeight) / 2);
      
      noteManagerWin.setBounds({
        x: restoreX,
        y: restoreY,
        width: restoreWidth,
        height: restoreHeight
      });
      
      isNoteManagerMaximized = false;
      noteManagerWin.webContents.send('window-maximized-state', false);
      
    } else {
      // 手动最大化窗口
      noteManagerWinBounds = currentBounds;
      
      noteManagerWin.setBounds({
        x: 0,
        y: 0,
        width: screenWidth,
        height: screenHeight
      });
      
      isNoteManagerMaximized = true;
      noteManagerWin.webContents.send('window-maximized-state', true);
    }
    
    return 'Window toggled';
  }
}

NoteController.toString = () => "[class NoteController]"
module.exports = NoteController;