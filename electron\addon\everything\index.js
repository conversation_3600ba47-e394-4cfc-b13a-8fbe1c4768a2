const { app: electronApp } = require("electron");
const Log = require("ee-core/log");
const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
const koffi = require("koffi");
const path = require("path");
const os = require("os");
const { exec } = require("child_process");

const maxResults = 100;

/**
 * Everything插件
 * @class
 */
class EveryThingAddon {
  constructor() {
    // this.create();
  }

  create() {
    Log.info("[addon:EveryThing] load");
    // 启动 Everything（使用参数最小化到后台）
    if (os.platform() !== "win32") {
      return;
    }
    const exe64Path = path.join(
      Ps.getExtraResourcesDir(),
      "exe/x64",
      "Everything.exe"
    );
    const exe86Path = path.join(
      Ps.getExtraResourcesDir(),
      "exe/x86",
      "Everything.exe"
    );
    const arch = os.arch(); // 'x64' or 'ia32'
    const everythingExe = arch === "x64" ? exe64Path : exe86Path;

    this.isEverythingRunning().then((isRunning) => {
      if (!isRunning) {
        // 使用 PowerShell 以管理员权限后台启动 Everything
        const psCommand = `Start-Process -FilePath "${everythingExe}" -ArgumentList "-startup" -Verb runAs -WindowStyle Hidden`;

        exec(`powershell -Command "${psCommand}"`, (error) => {
          if (error) {
          } else {
          }
        });
      }
      return;
    });
    if (alreadyRunning) {
      console.log("Everything 已在运行");
      return;
    }

    return;
    // 资源路径
    Log.info("🚀 ~ EveryThingAddon ~ create ~ dllPath:");
    const dllFile = "Everything64.dll";
    const dllPath = path.join(Ps.getExtraResourcesDir(), "dll", dllFile);
    Log.info("🚀 ~ EveryThingAddon ~ create ~ dllPath:", dllPath);
    const everything = koffi.load(dllPath);

    // 声明接口函数
    // 函数绑定（使用 stdcall）
    const funcs = {
      setSearch: everything.func("Everything_SetSearchW", "void", ["string"], {
        abi: "stdcall",
      }),
      query: everything.func("Everything_QueryW", "bool", ["bool"], {
        abi: "stdcall",
      }),
      getNumResults: everything.func("Everything_GetNumResults", "uint", [], {
        abi: "stdcall",
      }),
      getResultFullPath: everything.func(
        "Everything_GetResultFullPathNameW",
        "uint",
        ["uint", koffi.pointer("uint16"), "uint"],
        { abi: "stdcall" }
      ),
      cleanUp: everything.func("Everything_CleanUp", "void", [], {
        abi: "stdcall",
      }),
    };
    // everything.func('Everything_QueryW', 'bool', ['bool']);
    // everything.func('Everything_GetResultFullPathNameW', 'uint', ['uint', 'pointer', 'uint']);

    const searchStr = Buffer.from("123\0", "utf16le");
    // const searchPtr = koffi.as('pointer', searchStr);
    funcs.setSearch(searchStr);

    // 执行搜索
    const success = funcs.query(true);
    if (!success) {
      return [];
    }

    const numResults = Math.min(funcs.getNumResults(), maxResults);

    const results = [];

    // 每个路径的缓冲区（260 wchar）
    const wcharCount = 260;
    const buffer = Buffer.alloc(wcharCount * 2); // UTF-16

    for (let i = 0; i < numResults; i++) {
      buffer.fill(0); // 清空 buffer
      funcs.getResultFullPath(i, buffer, wcharCount);
      const filePath = buffer.toString("ucs2").replace(/\0/g, "");
      results.push(filePath);
    }

    // 释放资源（可选）
    funcs.cleanUp();

    // return results;
  }

  /**
   * 是否运行
   * @returns {true} - 是：否
   */
  isEverythingRunning() {
    return new Promise((resolve) => {
      exec("tasklist", (err, stdout) => {
        if (err) return resolve(false);
        const isRunning = stdout.toLowerCase().includes("everything.exe");
        resolve(isRunning);
      });
    });
  }

  // 封装搜索函数
}

EveryThingAddon.toString = () => "[class EveryThingAddon]";
module.exports = EveryThingAddon;
