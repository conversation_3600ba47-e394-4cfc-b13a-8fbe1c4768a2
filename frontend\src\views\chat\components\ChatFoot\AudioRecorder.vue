<template>
  <div class="relative">
    <div class="absolute -right-2 -top-14 cursor-pointer" @click="cancel" title="取消录音">
      <el-icon color="#666" size="20" class="hover: text-black">
        <close />
      </el-icon>
    </div>
    <div class="flex flex-col items-center justify-center text-red-500 mt-5" v-if="start">
      正在录制中
      <el-icon class="is-loading mt-2" color="#ff0000">
        <Loading />
      </el-icon>
    </div>
    <div v-else class="flex flex-col items-center justify-center text-red-500 mt-5">请点击开始录音录制音频</div>
    <div class="flex justify-between mt-10">
      <el-button type="primary" style="width: 48%" @click="startRecording" :disabled="subStr" >开始录音</el-button>
      <el-button type="primary" plain style="width: 48%" :disabled="!subStr" @click="stopRecording">发送录音</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import * as FileApi from "@/api/infra/file";
import { ElMessage } from "element-plus";
import { useTalkStore } from "@/stores/modules/talk";

const mediaRecorder = ref(null);
const talkStore = useTalkStore();
const isRecording = ref(false);
const recordedChunks = ref([]);
const recordedAudioUrl = ref("");
const start = ref(false);
const subStr = ref(false);
const contact = computed(() => {
  return talkStore.activeContact;
});
const props = defineProps({
  message: {
    type: Number,
    required: true
  }
});
onMounted(() => {

});
const emit = defineEmits(["modelval"]);
const cancel = () => {
  if (mediaRecorder.value && mediaRecorder.state === 'recording') {
    mediaRecorder.value.stop();
    isRecording.value = false;
    start.value = false;
    subStr.value = false;
    mediaRecorder.value.onstop = () => {
      recordedChunks.value = [];
      recordedAudioUrl.value = '';
    };
    emit("modelval", false);
  }else{
    emit("modelval", false);
  }
};

async function startRecording() {
  try {
    start.value = true;
    subStr.value = true;
    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
    mediaRecorder.value = new MediaRecorder(stream);

    mediaRecorder.value.ondataavailable = event => {
      if (event.data.size > 0) {
        recordedChunks.value.push(event.data);
      }
    };
    mediaRecorder.value.start();
    isRecording.value = true;
  } catch (error) {
    ElMessage.error('录音失败');
    start.value = false;
    subStr.value = false;
  }
}

function stopRecording() {
  if (mediaRecorder.value) {
    mediaRecorder.value.stop();
    isRecording.value = false;
    start.value = false;
    subStr.value = false;
    mediaRecorder.value.onstop = () => {
      const blob = new Blob(recordedChunks.value, { type: "audio/wav" });
      recordedAudioUrl.value = URL.createObjectURL(blob);
      recordedChunks.value = [];
      sendRecordedAudioToServer(blob);
    };
  }
}

async function sendRecordedAudioToServer(blob) {
  if (Number(contact.value.secret) >= Number(props.message)) {
    const formData = new FormData();
    formData.append("file", blob, new Date().getTime()+'.wav');
    FileApi.updateFile(formData)
      .then(res => {
        if (res.code === 0) {
          emit("modelval", false, res.data.id);
        } else {
          ElMessage.error("音频文件上传失败,请重新录制");
          emit("modelval", false);
        }
      })
      .catch(res => {
        ElMessage.error("音频文件上传失败，请重新录制");
        emit("modelval", false);
      });
  } else {
    emit("modelval", false);
    ElMessage.error("音频密级不能大于群密级,请重新选择录制!");
  }
}
</script>
