<template>
  <div class="search-drawer">
    <slot :open="openDialog" :data="searchData"></slot>
    <el-dialog v-model="visible" :show-close="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'search']" class="icon" />
          <span class="title">搜索</span>
        </div>
      </template>
      <div class="rounded-xl">
        <div class="mb-2">
          <el-input v-model="searchData" placeholder="请输入联系人或群组名称" @keydown.enter="getSearchResultsFn">
            <template #suffix>
              <font-awesome-icon :icon="['fas', 'search']" class="cursor-pointer text-xl" @click="getSearchResultsFn" />
            </template>
          </el-input>
        </div>
        <el-tabs v-model="activeTab">
          <el-tab-pane name="0" v-if="resultFile.length > 0">
            <template #label>
              <el-badge :value="resultFile.length" :offset="[10, 0]"> 本地文件 </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane name="1" v-if="msgList.length > 0">
            <template #label>
              <el-badge :value="msgList.length" :offset="[10, 0]"> 聊天记录 </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane name="2" v-if="resultLink.length > 0">
            <template #label>
              <el-badge :value="resultLink.length" :offset="[10, 0]"> 常用链接 </el-badge>
            </template>
          </el-tab-pane>

          <el-tab-pane name="3" v-if="resultGroup.length > 0">
            <template #label>
              <el-badge :value="resultGroup.length" :offset="[10, 0]"> 群组 </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane name="4" v-if="resultSingleInner.length > 0">
            <template #label>
              <el-badge :value="resultSingleInner.length" :offset="[10, 0]"> 组织架构 </el-badge>
            </template>
          </el-tab-pane>
          <el-tab-pane name="5" v-if="resultSingleOuter.length > 0">
            <template #label>
              <el-badge :value="resultSingleOuter.length" :offset="[10, 0]"> 组织外联系人 </el-badge>
            </template>
          </el-tab-pane>
        </el-tabs>
        <el-scrollbar>
          <div v-if="activeTab == '0'">
            <FileContact :user="resultFile" />
          </div>
          <div class="space-y-2" v-if="activeTab == '1'">
            <div
              v-for="item in msgList"
              class="msg-item flex items-center gap-3 rounded-lg p-2 hover:bg-gray-100 cursor-pointer"
              @click="gotoChat(item.contact)"
            >
              <div @click.stop>
                <DynamicAvatar
                  :id="item.contact.contactId"
                  :data-info="item.contact"
                  :relation-name="item.contact.contactName"
                  :type-avatar="item.contact.chatType"
                  :size="40"
                />
              </div>
              <div class="flex-1">
                <div class="text-sm font-medium text-gray-900 mr-2">{{ item.contact.contactName }}</div>
                <div class="text-xs text-gray-500">{{ item.num == 1 ? item.msg : `${item.num}条相关的聊天记录` }}</div>
              </div>
            </div>
          </div>
          <div v-if="activeTab == '2'">
            <LongLink :user="resultLink" @dia-close="closeDialog" />
          </div>
          <div v-if="activeTab == '3'">
            <GroupContact :user="resultGroup" :ifSearch="true" @click="handleClick" @search-click="handleClick" />
          </div>
          <div v-if="activeTab == '4'">
            <SingleContact :user="resultSingleInner" class="search-inner" @click="handleClick" @search-click="handleClick" />
          </div>
          <div v-if="activeTab == '5'">
            <FriendContact :user="resultSingleOuter" class="search-outer" @click="handleClick" @search-click="handleClick" />
          </div>
        </el-scrollbar>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { getChatSingle, getChatGroup, getAllFriend } from "@/api/modules/contact";
import { LinkApi } from "@/api/modules/link";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useRecentStore } from "@/stores/modules/recent";
import SingleContact from "@/components/ContactComp/SingleContact.vue";
import FriendContact from "@/components/ContactComp/FriendContact.vue";
import GroupContact from "@/components/ContactComp/GroupContact.vue";
import FileContact from "@/components/ContactComp/FileContact.vue";
import LongLink from "@/components/ContactComp/LongLink.vue";
import DynamicAvatar from "../Avatar/DynamicAvatar.vue";
import * as dbApi from "@/utils/indexedDB";
import { ipcApiRoute } from "@/ipc/ipcApi";
import { ipc } from "@/utils/ipcRenderer";
import { useContactsStore } from "@/stores/modules/contacts";

const props = defineProps({
  data: {
    type: String,
    required: true
  }
});
const contactsStore = useContactsStore();
const router = useRouter();
const route = useRoute();
const userStore = useUserStore();
const talkStore = useTalkStore();
const recentStore = useRecentStore();

const activeTab = ref("1");
const searchData = ref("");
const msgList: any = ref([]);
const resultSingleInner = ref([]);
const resultSingleOuter = ref([]);
const resultGroup = ref([]);
const resultFile = ref([]);
const resultLink = ref([]);

const visible = ref(false);
const openDialog = () => {
  visible.value = true;
  if (!friendList.value) {
    getFriendList();
  }
  searchData.value = props.data;
  getSearchResultsFn();
};
const closeDialog = () => {
  visible.value = false;
  searchData.value = "";
  msgList.value = [];
  resultSingleInner.value = [];
  resultSingleOuter.value = [];
  resultGroup.value = [];
  resultFile.value = [];
  resultLink.value = [];
};

defineExpose({ openDialog });

const friendList: any = ref();
const getFriendList = async () => {
  const params = {
    userId: userStore.userId,
    status: "1"
  };
  const res: any = await getAllFriend(params);
  friendList.value = res.data;
};

const searchMsg = async () => {
  msgList.value = [];
  if (!searchData.value) return;
  let res = await dbApi.getDatabyMsg(searchData.value);
  let msgObj: any = {};
  res.forEach((item: any) => {
    if (msgObj[item.contact.contactId]) {
      msgObj[item.contact.contactId].num++;
    } else {
      msgObj[item.contact.contactId] = {
        contact: item.contact,
        msg: item.msg,
        num: 1
      };
    }
  });
  msgList.value = Object.values(msgObj);
};

const searchSingle = async () => {
  const params = {
    name: searchData.value
  };
  const { data } = await getChatSingle(params);
  if (data.list) {
    for (let item of data.list) {
      let fileds: any = ["avatar", 'isOnline']
      if (item.online === null) fileds = ["avatar"]
      // 更新用户状态
      contactsStore.updateContact(
        {
          id: item.id,
          name: item.name,
          avatar: item.avatar,
          secretLevel: item.secretLevel,
          isOnline: item.online == "on"
        },
        fileds
      );
    }
    
    contactsStore.updateSqliteData();
  }
  resultSingleInner.value = data.list.filter((item: any) => item.isExternal == "0");
  let arr = data.list.filter((item: any) => item.isExternal == "1");
  let obj: any = {};
  resultSingleOuter.value = arr.map((item: any) => {
    if (friendList.value?.some((item_: any) => item.id == item_.id)) {
      obj.ifOuter = true;
    } else {
      obj.ifOuter = false;
    }
    return {
      ...item,
      ...obj
    };
  });

  // ipc?.invoke(ipcApiRoute.search, params.name).then((res: any) => {
  //   resultFile.value = res;
  // });
};
const searchLink = async () => {
  const res: any = await LinkApi.getUserNewVisibleLinks(userStore.userInfo?.orgCode || "", searchData.value);
  resultLink.value = res.data || [];
};
const searchGroup = async () => {
  const params = {
    pageNo: 1,
    pageSize: -1,
    groupName: searchData.value
  };
  const { data } = await getChatGroup(params);
  data.list.forEach((item: any) => {
    item.chatType = 0;
  });
  resultGroup.value = data.list;
};

const getSearchResultsFn = async () => {
  activeTab.value = "0";
  searchMsg();
  await searchSingle();
  await searchGroup();
  await searchLink();
  if (resultFile.value.length > 0) {
    activeTab.value = "0";
  }
  if (resultFile.value.length == 0 && msgList.value.length > 0) {
    activeTab.value = "1";
  }
  if (resultFile.value.length == 0 && msgList.value.length == 0 && resultLink.value.length > 0) {
    activeTab.value = "2";
  }
  if (resultFile.value.length == 0 && msgList.value.length == 0 && resultLink.value.length == 0 && resultGroup.value.length > 0) {
    activeTab.value = "3";
  }
  if (
    resultFile.value.length == 0 &&
    msgList.value.length == 0 &&
    resultLink.value.length == 0 &&
    resultGroup.value.length == 0 &&
    resultSingleInner.value.length > 0
  ) {
    activeTab.value = "4";
  }
  if (
    resultFile.value.length == 0 &&
    msgList.value.length == 0 &&
    resultLink.value.length == 0 &&
    resultGroup.value.length == 0 &&
    resultSingleInner.value.length == 0 &&
    resultSingleOuter.value?.length > 0
  ) {
    activeTab.value = "5";
  }
  // console.log(activeTab.value)
  // console.log(resultFile.value.length)
  // console.log(msgList.value.length)
  // console.log(resultLink.value.length)
  // console.log(resultGroup.value.length)
  // console.log(resultSingleInner.value.length)
  // console.log(resultSingleOuter.value.length)
};

const handleClick = () => {
  closeDialog();
  if (!route.path.includes("chat")) {
    router.push({ name: "chat" });
  }
};

const gotoChat = async (contact: any) => {
  const params = {
    senderId: userStore.userId,
    contactId: contact.contactId,
    chatType: contact.chatType,
    avatar: contact.avatar,
    contactName: contact.contactName,
    secret: contact.secret
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(contact.contactId);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
    handleClick();
  }
};
</script>

<style lang="scss" scoped>
.search-drawer {
  :deep(.el-collapse) {
    @apply border-0;

    .el-collapse-item__content {
      @apply pb-2;
    }
  }

  :deep(.el-scrollbar) {
    height: 400px;
  }
}

.dark {
  .msg-item {
    @apply hover:bg-gray-700;

    h4 {
      @apply text-white;
    }

    p {
      @apply text-gray-400;
    }
  }
}
</style>
