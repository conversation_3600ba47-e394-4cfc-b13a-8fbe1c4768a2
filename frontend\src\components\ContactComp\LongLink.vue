<template>
  <div class="p-2">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div class="app-grid">
      <div v-for="item in user as any" :key="item.id" class="app-card" @click="openLinkInBrowser(item)">
        <!-- 第一行：图标和名称 -->
        <div class="app-header-row">
          <div class="app-icon-wrapper">
            <div class="app-icon-avatar" :style="{ background: generateAvatarColor(item.title ? item.title.charAt(0) : 'A') }">
              {{ item.title ? item.title.charAt(0) : "A" }}
            </div>
          </div>

          <div class="app-title-wrapper">
            <h3 class="app-name">{{ item.title }}</h3>
            <span class="app-type">{{ item.personal === "1" ? "个人应用" : "系统应用" }}</span>
          </div>
        </div>

        <!-- 第二行：描述 -->
        <div class="app-description-row">
          <p class="app-description">{{ item.description || "这是一个实用的应用工具" }}</p>
        </div>

        <!-- 第三行：所属组织和更新时间 -->
        <div class="app-metadata-row">
          <div class="metadata-item">
            <el-icon class="metadata-icon"><Location /></el-icon>
            <span class="metadata-text">{{ item.orgCode || "系统应用" }}</span>
          </div>
          <div class="metadata-item">
            <el-icon class="metadata-icon"><VideoPlay /></el-icon>
            <span class="metadata-text">{{ formatDate(new Date()) }}</span>
          </div>
        </div>

        <!-- 第四行：启动按钮和启动方式图标 -->
        <div class="app-action-row">
          <button
            class="launch-button"
            :class="item.state === '1' ? 'launch-enabled' : 'launch-disabled'"
            @click.stop="openChat(item.userId)"
          >
            <el-icon class="launch-icon">
              <VideoPlay v-if="item.state === '1'" />
              <VideoPause v-else />
            </el-icon>
            <!-- <span class="launch-text">{{ item.state === '1' ? '启动' : '未启用' }}</span> -->
            <span class="launch-text">联系客服</span>
          </button>

          <!-- 启动方式图标 -->
          <div class="launch-methods" v-if="item.state === '1'">
            <div class="launch-method-item" @click.stop="handleLaunchMethod(item, item.image)" title="启动">
              <img :src="linkImgUrl[item.image]" class="method-icon" />
            </div>
          </div>
        </div>
        <!-- 悬停效果 -->
        <div class="hover-effect"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts" name="FileContact">
import { ref, onMounted } from "vue";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import { useTalkStore } from "@/stores/modules/talk";
import { getPersonInfo } from "@/api/modules/contact";
import NoData from "@/components/NoData/index.vue";
import { useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { formatDate } from "@/utils/formatTime";
import { LinkApi, AppLinkRespVO } from "@/api/modules/link";
import { ipc, isEE } from "@/utils/ipcRenderer";

const userStore = useUserStore();
const recentStore = useRecentStore();
const talkStore = useTalkStore();
const router = useRouter();

defineProps({
  user: {
    type: Array
  }
});
const linkImgUrl: any = ref({});
const getLinkImgUrl = () => {
  const emojiModules = import.meta.glob("@/assets/link/*.png", { eager: true, import: "default" });

  Object.keys(emojiModules).forEach(path => {
    // 获取文件名（不含扩展名）
    const fileName = path.split("/").pop()?.split(".")[0];
    if (fileName) {
      linkImgUrl.value[fileName] = emojiModules[path];
    }
  });
};
const emitInfo = defineEmits(["dia-close"]);
/** 启动应用 */
const handleLaunch = async (item: AppLinkRespVO) => {
  if (item.state !== "1") {
    ElMessage.warning("应用未启用，无法启动");
    return;
  }

  try {
    // 检查访问权限
    const res = await LinkApi.checkLinkPermission(item.id, item.orgCode);
    if (res.data?.code === 0 && res.data?.data) {
      // TODO: 实现应用启动逻辑
      ElMessage.success(`正在启动应用：${item.title}`);
    } else {
      ElMessage.warning("您没有权限访问此应用");
    }
  } catch (error) {
    ElMessage.error("应用启动失败");
  }
};

/** 通过指定方式启动应用 */
const handleLaunchMethod = async (item: AppLinkRespVO, method: string) => {
  if (item.state !== "1") {
    ElMessage.warning("应用未启用，无法启动");
    return;
  }

  try {
    // 检查访问权限
    const res = await LinkApi.checkLinkPermission(item.id, item.orgCode);
    if (res.data?.code === 0 && res.data?.data) {
      switch (method) {
        case "chrome":
          ElMessage.success(`正在通过Chrome浏览器启动：${item.title}`);
          // TODO: 实现Chrome启动逻辑
          break;
        case "firefox":
          ElMessage.success(`正在通过Firefox浏览器启动：${item.title}`);
          // TODO: 实现Firefox启动逻辑
          break;
        case "local":
          ElMessage.success(`正在本地启动：${item.title}`);
          // TODO: 实现本地启动逻辑
          break;
        default:
          ElMessage.error("不支持的启动方式");
      }
    } else {
      ElMessage.warning("您没有权限访问此应用");
    }
  } catch (error) {
    ElMessage.error("应用启动失败");
  }
};
/** 根据字符生成固定颜色 */
const generateAvatarColor = (char: string) => {
  // 定义一组更深色调的美观渐变色
  const colorPalettes = [
    "linear-gradient(135deg, #4c63d2 0%, #5a4fcf 100%)", // 深紫蓝
    "linear-gradient(135deg, #e91e63 0%, #c2185b 100%)", // 深粉红
    "linear-gradient(135deg, #2196f3 0%, #1976d2 100%)", // 深天蓝
    "linear-gradient(135deg, #009688 0%, #00695c 100%)", // 深青绿
    "linear-gradient(135deg, #ff5722 0%, #d84315 100%)", // 深橙红
    "linear-gradient(135deg, #673ab7 0%, #512da8 100%)", // 深紫色
    "linear-gradient(135deg, #e91e63 0%, #ad1457 100%)", // 深玫红
    "linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%)", // 深紫罗兰
    "linear-gradient(135deg, #ff9800 0%, #f57c00 100%)", // 深橙色
    "linear-gradient(135deg, #4caf50 0%, #388e3c 100%)", // 深绿色
    "linear-gradient(135deg, #ff6f00 0%, #e65100 100%)", // 深橙黄
    "linear-gradient(135deg, #795548 0%, #5d4037 100%)", // 深棕色
    "linear-gradient(135deg, #607d8b 0%, #455a64 100%)", // 深蓝灰
    "linear-gradient(135deg, #3f51b5 0%, #303f9f 100%)", // 深靛蓝
    "linear-gradient(135deg, #ff5722 0%, #bf360c 100%)", // 深红橙
    "linear-gradient(135deg, #8bc34a 0%, #689f38 100%)" // 深草绿
  ];

  // 根据字符的Unicode码点生成固定索引
  const charCode = char.charCodeAt(0);
  const index = charCode % colorPalettes.length;

  return colorPalettes[index];
};

/** 通过浏览器打开链接 */
const openLinkInBrowser = (item: AppLinkRespVO) => {
  if (!item.linkAddress) {
    ElMessage.warning("链接地址不存在");
    return;
  }

  // 确保链接以http://或https://开头
  let url = item.linkAddress;
  if (!url.startsWith("http://") && !url.startsWith("https://")) {
    url = "https://" + url;
  }

  try {
    // 如果是Electron环境，使用IPC调用本地浏览器
    if (isEE && ipc) {
      // 使用默认浏览器打开链接
      ipc
        .invoke("os/openUrlByLocalBrowser", {
          browserType: "default", // 使用默认浏览器
          url: url
        })
        .then(() => {
          ElMessage.success(`正在打开：${item.title}`);
        })
        .catch((error: any) => {
          console.error("调用本地浏览器失败:", error);
          // 如果IPC调用失败，回退到window.open
          window.open(url, "_blank");
          ElMessage.success(`正在打开：${item.title}`);
        });
    } else {
      // 非Electron环境，使用传统的window.open
      window.open(url, "_blank");
      ElMessage.success(`正在打开：${item.title}`);
    }
  } catch (error) {
    console.error("打开链接失败:", error);
    ElMessage.error("打开链接失败");
  }
};
/** 客户服务 */
const openChat = async (userId: any) => {
  if (!userId) {
    ElMessage.error("无用户userId");
    return;
  }
  const res: any = await getPersonInfo(userId);
  if (res.code == 0) {
    const data = res.data;
    const params = {
      senderId: userStore.userId,
      contactId: data.id,
      chatType: 0,
      avatar: data.avatar,
      contactName: data.name,
      secret: data.secretLevel
    };
    await recentStore.addListRecents(params);
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
    emitInfo("dia-close", false);
    router.push({ name: "chat" });
  }
};

onMounted(() => {
  getLinkImgUrl();
});
</script>

<style scoped lang="scss">
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 24px;
}

// 应用卡片样式
.app-card {
  position: relative;
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow:
    0 4px 6px rgba(0, 0, 0, 0.05),
    0 10px 15px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 200px;

  &:hover {
    transform: translateY(-8px);
    box-shadow:
      0 4px 10px rgba(0, 0, 0, 0.15),
      0 8px 15px rgba(0, 0, 0, 0.1);
    border-color: rgba(102, 126, 234, 0.2);

    .hover-effect {
      opacity: 1;
    }

    .app-icon,
    .app-icon-avatar {
      transform: scale(1.1);
    }

    .launch-button {
      transform: translateY(-2px);
    }
  }
}
// 第一行：图标和名称
.app-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.app-icon-wrapper {
  position: relative;
  width: 48px;
  height: 48px;
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
}

.app-icon {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.app-icon-avatar {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 700;
  color: white;
  text-transform: uppercase;
  transition: transform 0.3s ease;
}

.app-title-wrapper {
  flex: 1;
  min-width: 0;

  .app-name {
    font-size: 16px;
    font-weight: 700;
    color: #1f2937;
    margin: 0 0 4px 0;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .app-type {
    font-size: 11px;
    color: #6b7280;
    font-weight: 500;
    background: #f3f4f6;
    padding: 2px 6px;
    border-radius: 4px;
    display: inline-block;
  }
}

// 第二行：描述
.app-description-row {
  margin-bottom: 12px;

  .app-description {
    margin: 0;
    font-size: 13px;
    line-height: 1.4;
    color: #6b7280;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    min-height: 36px;
  }
}

// 第三行：元数据
.app-metadata-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .metadata-item {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 11px;
    color: #6b7280;

    .metadata-icon {
      font-size: 12px;
      color: #9ca3af;
    }

    .metadata-text {
      font-weight: 500;
    }
  }
}

// 第四行：启动按钮和启动方式
.app-action-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  margin-top: auto;
}

.launch-button {
  flex: 1;
  height: 36px;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;

  &.launch-enabled {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.25);

    &:hover {
      box-shadow: 0 2px 8px rgba(102, 126, 234, 0.35);
      transform: translateY(-1px);
    }

    &:active {
      transform: scale(0.98);
    }
  }

  &.launch-disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;

    &:hover {
      background: #e5e7eb;
    }
  }

  .launch-icon {
    font-size: 16px;
  }

  .launch-text {
    font-weight: 600;
  }
}

// 启动方式图标
.launch-methods {
  display: flex;
  align-items: center;
  gap: 8px;
}

.launch-method-item {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: #f1f5f9;
    border-color: #667eea;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: scale(0.95);
  }

  .method-icon {
    width: 18px;
    height: 18px;
    object-fit: contain;

    &.local-icon {
      font-size: 18px;
      color: #667eea;
    }
  }
}
</style>
