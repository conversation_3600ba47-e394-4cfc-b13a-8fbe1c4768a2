<template>
  <div>
    <el-scrollbar @scroll="handleScroll" ref="scrollitem">
      <div v-if="lists.length == 0">
        <NoData />
      </div>
      <div v-else v-for="(item,index) in lists" :key="index" class="msg-box p-2 mb-2 border border-gray-200 rounded-md flex">
        <div>
          <DynamicAvatar :id="item.sender" :data-info="item" :relation-name="item.senderName" :type-avatar="0" :size="32" />
        </div>
        <div class="flex-1 px-2 text-xs mt-2" style="width: 350px">
          <div class="mb-2 flex items-center">
            <span class="w-10 truncate">{{ item.senderName }}</span>
            <LevelBtn v-if="item.secret" :message="item.secret" class="mx-2" />
            <span class="float-right text-gray-400">{{ dayjs(item.createTime).format("YYYY-MM-DD HH:mm:ss") }}</span>
          </div>
          <!-- 文本 -->
          <MessageText :data-item="item" v-if="item.msgType == 0 || item.msgType == 7" />
          <!-- 文件 -->
          <MessageFile :data-item="item" v-if="item.msgType == 1" />
          <!-- 图片 -->
          <MessageImg :data-item="item" v-if="item.msgType == 2" />
          <!-- 音频 -->
          <MessageAudio :data-item="item" v-if="item.msgType == 3" />
          <!-- 表情 -->
          <MessageEmo :data-item="item" v-if="item.msgType == 8" />
          <!-- 投票 -->
          <MessageVote :vote-id="item.msg" v-if="item.msgType == 4" />
          <!-- 接龙 -->
          <MessageRelay :relay-id="item.msg" v-if="item.msgType == 5" />
          <!-- 公文 -->
          <MessageOfficialDoc :doc-id="item.msg" v-if="item.msgType == 9" />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts" name="ChatHistoryItem">
import { ref, computed } from "vue";
import LevelBtn from "@/components/LevelBtn/index.vue";
import * as historyApi from "@/api/modules/history";
import { useTalkStore } from "@/stores/modules/talk";
import { useHistoryStore } from "@/stores/modules/history";
import dayjs from "dayjs";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import MessageFile from "@/views/chat/components/ChatContent/MessageFile.vue";
import MessageImg from "@/views/chat/components/ChatContent/MessageImg.vue";
import MessageAudio from "@/views/chat/components/ChatContent/MessageAudio.vue";
import MessageText from "@/views/chat/components/ChatContent/MessageText.vue";
import MessageEmo from "@/views/chat/components/ChatContent/MessageEmo.vue";
import MessageVote from "@/views/chat/components/ChatContent/MessageVote.vue";
import MessageRelay from "@/views/chat/components/ChatContent/MessageRelay.vue";
import MessageOfficialDoc from "@/views/chat/components/ChatContent/MessageOfficialDoc.vue";
import NoData from "@/components/NoData/index.vue";
import { ElMessage } from "element-plus";

const talkStore = useTalkStore();
const historyStore = useHistoryStore();

const props = defineProps({
  msgType: {
    type: String,
    required: true
  },
  searchQuery: {
    type: String,
    required: true
  }
});

const contact = computed(() => talkStore.activeContact);
const pageNo = ref(1);
const pageSize = 20;
const loading = ref(false);

const lists: any = computed(() => {
  let data = JSON.stringify(historyStore.msgHistory[talkStore.activeChatId] || []);
  let data_ = JSON.parse(data).reverse();
  let arr = [];
  if (data.length == 0) {
    return [];
  } else {
    arr = data_.filter((item: any) => item.msg?.includes(props.searchQuery));
    if (props.msgType == "all") {
      return arr;
    } else if (props.msgType == "1") {
      return arr.filter((item: any) => item.msgType == 1);
    } else if (props.msgType == "3") {
      return arr.filter((item: any) => item.msgType == 3);
    } else if (props.msgType == "activity") {
      return arr.filter((item: any) => item.msgType == 4 || item.msgType == 5 || item.msgType == 9);
    } else {
      return arr.filter((item: any) => item.msgType == 2);
    }
  }
});

const getMsgHistory = async (contactId: any) => {
  if (loading.value) return;
  loading.value = true;
  pageNo.value = Math.floor(lists.value.length / pageSize) + 1;
  const params = {
    receiver: contactId,
    pageNo: pageNo.value,
    pageSize: pageSize,
    msg: props.searchQuery
  };
  let res: any = [];
  if (contact.value.chatType) {
    res = await historyApi.getGroupMsgHistory(params);
  } else {
    res = await historyApi.getUserMsgHistory(params);
  }
  if (res.code == 0) {
    res.data.list.forEach((item: any) => {
      historyStore.setMsgHistory(contactId, item.id, item);
    });

    if (historyStore.msgHistory[contactId] && historyStore.msgHistory[contactId].length == res.data.total) {
      ElMessage.warning("~ 没有更多数据了 ~");
    }
    loading.value = false;
  }
};
const scrollitem: any = ref(null);
let timer: NodeJS.Timeout | null = null;
const handleScroll = () => {
  if (timer) clearTimeout(timer);
  const { scrollTop, scrollHeight, clientHeight } = scrollitem.value.wrapRef;
  if (scrollTop + clientHeight >= scrollHeight - 20) {
    timer = setTimeout(() => {
      getMsgHistory(talkStore.activeChatId);
    }, 200);
  }
};
</script>

<style lang="scss" scoped>
.el-scrollbar {
  height: calc(100vh - 240px);
  overflow-x: hidden;
}
.dark {
  .msg-box {
    @apply border-gray-700;
  }
}
</style>
