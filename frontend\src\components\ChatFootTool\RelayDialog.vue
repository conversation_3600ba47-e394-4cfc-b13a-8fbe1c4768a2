<template>
  <div class="dialog-box">
    <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog">
      <template #header>
        <button class="close-btn" @click="closeDialog">
          <font-awesome-icon :icon="['fas', 'times']" />
        </button>
        <div class="flex items-center gap-2">
          <font-awesome-icon :icon="['fas', 'list']" class="icon" />
          <span class="title">接龙</span>
        </div>
      </template>
      <div>
        <el-form :model="formData" :rules="state.rules" label-position="top" require-asterisk-position="right" ref="ruleForm">
          <el-form-item label="接龙名称" prop="title">
            <el-input v-model="formData.title" placeholder="请输入接龙名称"></el-input>
          </el-form-item>
          <el-form-item label="接龙描述" prop="description">
            <el-input v-model="formData.description" type="textarea" placeholder="请输入描述信息"></el-input>
          </el-form-item>
          <div class="flex">
            <el-form-item label="接龙创建日期 " prop="createdAt" class="mr-5">
              <el-date-picker
                v-model="formData.createdAt"
                type="datetime"
                placeholder="接龙创建日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="接龙截止日期" prop="deadline">
              <el-date-picker
                v-model="formData.deadline"
                type="datetime"
                placeholder="接龙截止日期"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              ></el-date-picker>
            </el-form-item>
          </div>
          <el-form-item label="最大接龙人数" prop="maxParticipants">
            <el-input-number
              v-model="formData.maxParticipants"
              :min="1"
              :max="state.max"
              :precision="0"
              :step="1"
              type="textarea"
              placeholder="请输入最大接龙人数"
            />
          </el-form-item>
          <!-- <el-form-item label="标签" prop="tags">
            <el-select v-model="formData._tags" multiple placeholder="请选择标签" class="custom-select">
              <el-option v-for="item in tags" :key="item" :label="item" :value="item" class="custom-option" />
            </el-select>
          </el-form-item> -->
        </el-form>
        <div class="bottom-0 right-0">
          <el-button type="primary" :loading="relayLoading" @click="submitForm(ruleForm)">提交</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { createSolitaire } from "@/api/modules/contact";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { FormInstance, ElMessage, dayjs } from "element-plus";
//获取store实例
const userStore = useUserStore();
const talkStore = useTalkStore();
const contactStore = useContactStore();
const relayLoading = ref(false);
//表单实例
const ruleForm: any = ref(null);
const visible = ref(false);
const tags = ref(["假期", "出行", "美食"]);

const formData = ref({
  title: "",
  description: "",
  maxParticipants: 1,
  creatorId: "",
  createdAt: "",
  deadline: "",
  businessId: "",
  _tags: [] as string[],
  tags: ""
});

const checkDeadline = (_rule: any, value: any, callback: any) => {
  if (formData.value.createdAt && dayjs(value).valueOf() <= dayjs(formData.value.createdAt).valueOf()) {
    return callback(new Error("接龙截止日期必须晚于创建日期"));
  }

  callback();
};

const state = reactive({
  max: 1,
  rules: {
    title: [{ required: true, message: "请输入接龙名称", trigger: "change" }],
    description: [{ required: true, message: "请输入描述信息", trigger: "change" }],
    createdAt: [{ required: true, message: "请选择接龙创建日期", trigger: "change" }],
    deadline: [
      { required: true, message: "请选择接龙截止日期", trigger: "change" },
      { validator: checkDeadline, trigger: "change" }
    ],
    maxParticipants: [{ required: true, message: "请输入最大接龙人数", trigger: "change" }]
  }
});

const emit = defineEmits(["send-message"]);

watch(visible, newVal => {
  if (newVal) {
    const { activeChatId = "" } = talkStore;
    const { groupInfo } = contactStore;

    let max = groupInfo[activeChatId]?.memberNum ?? 1;

    state.max = max;
    formData.value.maxParticipants = max;
    formData.value.businessId = activeChatId;
    formData.value.creatorId = userStore.userId;
  } else {
    formData.value.maxParticipants = 1;
    formData.value.businessId = "";
    formData.value.creatorId = "";
    formData.value._tags = [];
    formData.value.tags = "";
    if (ruleForm.value) {
      ruleForm.value.resetFields();
    }
  }
});

const submitForm = async (ruleFormRef: FormInstance | null) => {
  if (ruleFormRef) {
    ruleFormRef.validate(async (valid: any) => {
      if (valid) {
        relayLoading.value = true;
        try {
          formData.value.tags = formData.value._tags.join(",");
          const res: any = await createSolitaire(formData.value);
          if (res.code == 0) {
            ElMessage.success("保存成功");
            emit("send-message", res.data, 5);
            closeDialog();
          }
        } finally {
          relayLoading.value = false;
        }
      } else {
        ElMessage.error("请检查表单项!");
      }
    });
  }
};
const openDialog = () => {
  visible.value = true;
};
const closeDialog = () => {
  visible.value = false;
};
defineExpose({ openDialog, closeDialog });
</script>

<style lang="scss" scoped>
.el-form-item {
  @apply block;

  :deep(.el-form-item__content) {
    flex-wrap: nowrap;
  }
}
</style>
