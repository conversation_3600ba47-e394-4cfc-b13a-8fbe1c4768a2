const CoreWindow = require("ee-core/electron/window");
const Log = require("ee-core/log");
const Conf = require("ee-core/config");
const Ps = require("ee-core/ps");
const path = require("path");
const { app, Notification } = require("electron");

/**
 * 临时方法，处理原逻辑，所有消息通过console.log(),进行监控
 * 800100  收到消息 {code，title，content}
 * 800101  窗口变化事件{code，type}（最大化、最小化、关闭）
 *
 */

class NotificationAddon {
  constructor() {
    this.notice = null;
    this.iconPathNormal = "";
  }

  create() {
    // 通过console监听完成系统提示
    Log.info("[addon:notification] load");
    // 设置通知标题，否则显示electron app.Electron
    app.setAppUserModelId("云雀");
    const mainWindow = CoreWindow.getMainWindow();
    if (Conf.getValue("addons.notice.console_notice")) {
      mainWindow.webContents.on(
        "console-message",
        async (e, level, message, line, sourceId) => {
          try {
            if (/\{.*?"code":800100,.*?\}/.test(message)) {
              this.noticeInfo(message);
              // todo 暂时的方法
            } else if (/\{.*?"code":800200,.*?\}/.test(message)) {
              const msg = JSON.parse(message);
              if (msg.type == "max") {
                if (mainWindow.isMaximized()) {
                  mainWindow.unmaximize();
                } else {
                  mainWindow.maximize();
                }
              } else if (msg.type == "help") {
              } else if (msg.type == "close") {
                mainWindow.minimize();
              } else {
                Log.error("Invalid message format:", msg);
              }
            } else if (/\{.*?"code":4700,.*?\}/.test(message)) {
              const msg = JSON.parse(message);
              const oldMsg = {
                title: msg.title,
                body: msg.props.body,
              };
              this.noticeInfo(JSON.stringify(oldMsg));
            } else {
            }
          } catch (err) {
            Log.error("Error parsing message:", err);
          }
        }
      );
    } else {
      return;
    }
  }

  noticeInfo(message) {
    const mainWindow = CoreWindow.getMainWindow();
    const msg = JSON.parse(message);
    const cfg = Conf.getValue("addons.notice");
    const option = {
      title: msg.title,
      body: msg.content.substring(0, 15), // Limit to 20 characters
      silent: true,
      icon: "",
      timeout: 5000, // Display for 5 seconds
    };

    switch (msg.type) {
      case 1:
        option.icon = path.join(Ps.getHomeDir(), cfg.user_icon);
        break;
      case 2:
        option.icon = path.join(Ps.getHomeDir(), cfg.group_icon);
        break;
      case 3:
        option.icon = path.join(Ps.getHomeDir(), cfg.system_icon);
        break;
      default:
        Log.error("Invalid message format:", msg);
        break;
    }
    const notification = new Notification(option);
    notification.on("click", () => {
      mainWindow.show();
    });
    notification.show();
  }
}

NotificationAddon.toString = () => "[class NotificationAddon]";
module.exports = NotificationAddon;
