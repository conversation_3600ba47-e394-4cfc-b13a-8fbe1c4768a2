const { Service } = require("ee-core");
const Ps = require("ee-core/ps");
const CoreWindow = require("ee-core/electron/window");
const path = require("path");
const os = require("os");
const { app } = require("electron");
const Services = require("ee-core/services");
const { v4: uuidv4 } = require("uuid");
const fs = require("fs");
const axios = require("axios");

// const fileStatusChannel = "controller.download.downloadProgress";
const addDownloadChannel = "controller.download.addDownload";
const downloadProgress = "controller.download.downloadProgress";
const downloadCompleted = "controller.download.downloadCompleted";
const downloads = [];
class FileManagerService extends Service {
  constructor(ctx) {
    super(ctx);
  }

  // /**
  //  * 向前端发消息
  //  */
  // sendStatusToWindow(content = {}) {
  //   const textJson = JSON.stringify(content);
  //   const win = CoreWindow.getMainWindow();
  //   win.webContents.send(fileStatusChannel, textJson);
  // }

  /**
   * 获取下载中的字节数据
   * @param {Array} data - 下载项数组
   * @returns {{receivedBytes: number, totalBytes: number}}
   */
  getDownloadBytes(data) {
    const allBytes = data.reduce(
      (prev, current) => {
        if (current.state === "progressing") {
          prev.receivedBytes += current.receivedBytes;
          prev.totalBytes += current.totalBytes;
        }
        return prev;
      },
      { receivedBytes: 0, totalBytes: 0 }
    );
    return allBytes;
  }

  /**
   * 获取下载项
   * @param {Array} data - 下载记录数组
   * @param {string} id - 下载项 id
   * @returns {Object|null}
   */
  getDownloadItem(data, id) {
    const newData = data.filter((item) => item.id === id);
    if (!newData.length) return null;
    return newData[0]._sourceItem || null;
  }

  /**
   * 获取下载项下标
   * @param {Array} data - 下载记录数组
   * @param {string} id - 下载项 id
   * @returns {number}
   */
  getDownloadIndex(data, id) {
    return data.findIndex((item) => item.id === id);
  }

  /**
   * 是否存在下载项
   * @param {string} url - 下载地址
   * @param {Array} data - 下载记录数组
   * @returns {Object|null}
   */
  isExistItem(url, data) {
    const item = data.filter((d) => d.url === url);
    return item.length ? item[0] : null;
  }

  /**
   * 下载文件
   * @param {string} url - 下载地址
   */
  download(url) {
    const win = CoreWindow.getMainWindow();
    if (!win) return;
    win.webContents.downloadURL(url);
  }

  /**
   * 保存下载记录到 store
   * @param {Array} data - 下载项数组
   */
  setDownloadStore(data) {
    // store.set("downloadManager", data);
  }

  /**
   * 获取下载记录
   * @returns {Array}
   */
  getDownloadStore() {
    return this.deleteSourceItem(
      Services.get("database.larkfiledb").getAllLarkFiles()
    );
  }

  /**
   * 设置任务栏进度
   * @param {Array} data - 下载项数组
   * @param {Array} completedData - 已完成下载项 id 数组
   * @param {number} progress - 进度
   */
  setTaskbar(data, completedData, progress) {
    // const count = data.length - completedData.length;
    // if (win) {
    //   win.setProgressBar(count < 1 ? -1 : progress);
    // }
    // if (process.platform === "darwin") {
    //   app.badgeCount = count;
    // }
  }

  /**
   * 新增下载项
   * @param {DownloadItem} item - Electron 的 DownloadItem 对象
   * @param {WebContents} webContents - webContents
   * @returns {Promise<Object>} 返回添加的下载项数据
   */
  async addDownloadItem(item) {
    const win = CoreWindow.getMainWindow();
    const id = uuidv4();
    const fileUrl = item.getURL();

    const startTime = item.getStartTime() * 1000;
    const totalBytes = item.getTotalBytes();
    const configField =
      await Services.get("database.jsondb").getAllConfigField();
    const downloadDir = configField.fileSavePath;
    // const downloadDir = Ps.getUserHomeDir() + "/Downloads";

    const fileName = this.getFileName("", item.getFilename(), downloadDir);
    const savePath = path.join(downloadDir, fileName);

    item.setSavePath(savePath);

    // Services.get("database.larkfiledb").addLarkFile({
    //   userId: 123,
    //   file_id: id,
    //   file_name: fileName,
    //   download_url: fileUrl,
    //   download_status: item.getState() || 0,
    //   download_time: startTime,
    //   save_path: savePath,
    //   file_size: totalBytes,
    // });

    // Services.get("database.larkfiledb").getLarkFileByUserId(123);
    const fileIcon = await this.getFileIcon(savePath);

    const downloadItem = {
      file_id: id,
      url: fileUrl,
      icon: fileIcon,
      fileName,
      path: savePath,
      state: item.getState(),
      startTime,
      speed: 0,
      progress: 0,
      totalBytes,
      receivedBytes: item.getReceivedBytes(),
      paused: item.isPaused(),
      _sourceItem: item, // Electron 的 DownloadItem 对象
    };

    Services.get("database.larkfiledb").addLarkFile(downloadItem);
    // this.downloads.push(downloadItem);
    let downloadItems = this.deleteSourceItem(downloadItem);
    // 新下载任务创建完成，通知渲染进程
    win.webContents.send(addDownloadChannel, downloadItems);

    return downloadItems;
  }

  /**
   * 更新下载项数据
   * @param {Object} param - 参数对象
   * @param {*} param.item - Electron 的 DownloadItem 对象
   * @param {Object} param.downloadItem - 当前下载项数据
   * @param {Array} param.data - 下载文件详细数据数组
   * @param {number} param.prevReceivedBytes - 上一次的已接收字节数
   * @param {string} param.state - 下载状态
   * @returns {number} 返回最新已接收的字节数
   */
  updateDownloadItem({ item, downloadItem, data, prevReceivedBytes, state }) {
    // 获取最新的已接收字节数
    const receivedBytes = item.getReceivedBytes();

    // 更新 downloadItem 的相关属性
    downloadItem.receivedBytes = receivedBytes;
    downloadItem.state = state;
    downloadItem.progress =
      item.getTotalBytes() > 0 ? receivedBytes / item.getTotalBytes() : 0;
    downloadItem.paused = item.isPaused();
    downloadItem.speed = receivedBytes - prevReceivedBytes;

    // 找到下载项在数据数组中的索引
    const index = this.getDownloadIndex(data, downloadItem.id);
    if (index !== -1) {
      // 更新数据数组中的下载项
      data[index] = { ...downloadItem };
      // 保存更新后的下载记录到 store
      this.setDownloadStore(data);
    }

    return receivedBytes;
  }

  /**
   * 获取文件名，如果在给定目录中已存在同名文件，则在后缀增加自增数字
   * @param {string} fileName - 文件名
   * @param {string} defaultName - 默认文件名
   * @param {string} [dir] - 文件保存的目录，若提供则检查同名文件是否存在
   * @returns {string} 处理后的文件名
   */
  getFileName(fileName, defaultName, dir) {
    // 处理 Windows 不允许的字符
    fileName = fileName.replace(/(\/|\|?:|\?|\*|"|>|<|\|)/g, "") || defaultName;
    fileName = /^\.(.*)/.test(fileName) ? defaultName : fileName;

    // 获取文件后缀名
    let extName = this.getFileExt(fileName);
    if (!extName) {
      const ext = this.getFileExt(defaultName);
      fileName = `${fileName}.${ext}`;
      extName = this.getFileExt(fileName);
    }

    // 初步处理后的文件名
    let finalName = decodeURIComponent(fileName);

    // 如果提供了目录，检查是否已存在同名文件
    if (dir) {
      const baseName = path.basename(finalName, extName);
      let counter = 1;
      let candidate = finalName;
      while (fs.existsSync(path.join(dir, candidate))) {
        candidate = `${baseName}(${counter})${extName}`;
        counter++;
      }
      finalName = candidate;
    }

    return finalName;
  }

  /**
   * 获取文件后缀名
   * @param fileName - 文件名
   */
  getFileExt(fileName) {
    return path.extname(fileName);
  }

  /**
   * 实时更新下载项数据
   * @param {Object} downloadItem - 下载项数据
   * @param {WebContents} webContents - webContents
   */
  sendDownloadStatus(item, downloadItem, webContents) {
    const win = CoreWindow.getMainWindow();

    let startTime = Date.now();
    let lastBytesReceived = 0;

    // const item = downloadItem._sourceItem;
    item.on("updated", (event, state) => {
      const currentTime = Date.now();
      const bytesReceived = item.getReceivedBytes();
      const timeElapsed = (currentTime - startTime) / 1000; // 秒
      const speed = (bytesReceived - lastBytesReceived) / timeElapsed; // 字节/秒
      downloadItem.speed = speed;
      // 更新时间戳和接收字节数
      startTime = currentTime;
      lastBytesReceived = bytesReceived;

      downloadItem.receivedBytes = item.getReceivedBytes();

      downloadItem.progress = item.getReceivedBytes() / item.getTotalBytes();
      downloadItem.state = state;

      downloadItem.paused = item.isPaused();
      // this.setDownloadStore(this.downloads);
      win.webContents.send(downloadProgress, downloadItem);
    });

    item.once("done", (event, state) => {
      downloadItem.state = state;
      Services.get("database.larkfiledb").updateLarkFile({
        file_id: downloadItem.file_id,
        download_status: "2", // 已完成
      });
      win.webContents.send(downloadCompleted, downloadItem);
    });
  }

  /**
   * 初始化下载数据，并按开始时间倒序排列
   * @returns {Array} 下载记录数组
   */
  initDownloadData() {
    const data = FileManagerService.getDownloadStore().sort(
      (a, b) => Math.floor(b.startTime) - Math.floor(a.startTime)
    );
    return data;
  }

  /**
   * 移除对象或数组中的 _sourceItem 属性
   * @param {Object|Array} data - 单个对象或对象数组
   * @returns {Object|Array} 移除 _sourceItem 属性后的数据
   */
  deleteSourceItem(data) {
    if (Array.isArray(data)) {
      return data.map((item) => {
        const { _sourceItem, ...rest } = item;
        return rest;
      });
    } else if (typeof data === "object" && data !== null) {
      const { _sourceItem, ...rest } = data;
      return rest;
    }
    return data;
  }

  /**
   * 分页获取下载数据
   * @param {Array} data - 下载记录数组
   * @param {Object} options - 分页参数
   * @param {number} [options.pageIndex=1] - 当前页
   * @param {number} [options.pageCount=10] - 每页数量
   * @returns {Array} 分页后的下载记录数据
   */
  getDownloadData(data, { pageIndex = 1, pageCount = 10 } = {}) {
    const cleanData = FileManagerService.deleteSourceItem(data);
    const query = (pageIndex - 1) * pageCount;
    return query + pageCount >= cleanData.length
      ? cleanData.slice(query)
      : cleanData.slice(query, query + pageCount);
  }

  /**
   * 获取文件图标。
   * 系统关联图标
   * @param path - 文件路径
   */
  getFileIcon = async (path) => {
    const iconDefault = "./icon_default.png";
    if (!path) Promise.resolve(iconDefault);

    const icon = await app.getFileIcon(path, {
      size: "normal",
    });

    return icon.toDataURL();
  };

  delDownloadFileById(id) {
    Services.get("database.larkfiledb").deleteLarkFile(id);
  }

  pauseOrResumeDownloadFile(id) {
    const filelists = Services.get("database.larkfiledb").getAllLarkFiles();
    this.getDownloadItem(filelists, id).then((item) => {
      if (item) {
        item.isPaused() ? item.resume() : item.pause();
      }
    });
  }

  /**
   * 文件流式下载
   * @param {Object} params - 下载参数
   * @param {string} [params.fileId] - 文件id
   * @param {string} [params.fileName] - 文件名
   * @param {string} [params.fetchUrl] - 文件请求地址
   */
  async downloadFile(params) {
    // 获取主窗口实例
    const win = CoreWindow.getMainWindow();

    // 从配置文件中获取文件保存路径
    const configField =
      await Services.get("database.jsondb").getAllConfigField();
    const downloadDir = configField.fileSavePath;

    // 构建完整的文件保存路径
    const fileName = this.getFileName("", params.fileName, downloadDir);
    const savePath = path.join(downloadDir, fileName);

    // 获取文件图标
    const fileIcon = await this.getFileIcon(savePath);

    // 初始化下载项信息
    const downloadItem = {
      file_id: params.fileId,
      icon: fileIcon,
      fileName,
      path: savePath,
      startTime: Date.now(),
      speed: 0,
      progress: 0,
      totalBytes: 0,
      receivedBytes: 0,
      state: "pending",
    };

    // 下载成功的处理函数
    const downloadSuccess = () => {
      if (writer) writer.close();
      downloadItem.progress = 1;
      downloadItem.state = "success";
      win.webContents.send(downloadProgress, downloadItem);
    };

    // 下载失败的处理函数
    const downloadError = () => {
      if (writer) writer.close();
      downloadItem.state = "error";
      win.webContents.send(downloadProgress, downloadItem);
      fs.unlink(savePath, () => {});
    };

    // 创建一个写入流用于保存文件
    const writer = fs.createWriteStream(savePath);
    // 开启下载
    win.webContents.send(downloadProgress, downloadItem);

    let lastTime = Date.now();
    let lastReceivedBytes = 0;

    try {
      const response = await axios({
        method: "get",
        url: params.fetchUrl,
        responseType: "stream",
        onDownloadProgress: ({ loaded, total }) => {
          const currentTime = Date.now();
          const timeElapsed = (currentTime - lastTime) / 1000;
          const speed = (loaded - lastReceivedBytes) / timeElapsed;
          lastTime = currentTime;
          lastReceivedBytes = loaded;

          downloadItem.receivedBytes = loaded;
          downloadItem.totalBytes = total;
          downloadItem.speed = speed;
          const progress = Math.round((loaded * 100) / total) / 100;
          downloadItem.progress = progress === 1 ? 0.99 : progress;
          // 更新 downloadItem 状态
          win.webContents.send(downloadProgress, downloadItem);
        },
      });

      // 将响应数据通过管道写入文件
      response.data.pipe(writer);

      writer.on("finish", () => {
        downloadSuccess();
      });

      writer.on("error", (error) => {
        downloadError();
      });
    } catch (error) {
      downloadError();
    }
  }
}

FileManagerService.toString = () => "[class FileManagerService]";
module.exports = FileManagerService;
