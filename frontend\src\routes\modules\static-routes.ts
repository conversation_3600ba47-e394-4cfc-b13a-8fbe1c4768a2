import { HOME_URL, LOGIN_URL } from "@/config";
import { RouteRecordRaw } from "vue-router";

/**
 * staticRouter(静态路由)
 */
export const staticRoutes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: HOME_URL
  },
  {
    path: LOGIN_URL,
    name: "login",
    component: () => import("@/views/login/index.vue"),
    meta: {
      title: "登录"
    }
  },
  {
    path: "/layout",
    name: "layout",
    component: () => import("@/layouts/index.vue"),
    redirect: HOME_URL,
    children: []
  },
  {
    path: "/desktop",
    name: "桌面挂件",
    children: [
      {
        path: "floatingBall",
        name: "悬浮球",
        component: () => import("@/views/floatingBall/index.vue")
      },
      {
        path: "balldialog",
        name: "助手页面",
        component: () => import("@/views/floatingBall/balldialog.vue")
      },
      {
        path: "balldialog2",
        name: "助手页面2",
        component: () => import("@/views/floatingBall/balldialog2.vue")
      },
      {
        path: "ballsetting",
        name: "助手设置页面",
        component: () => import("@/views/floatingBall/ballsetting.vue")
      },
      {
        path: "viewdialog",
        name: "助手展示页面",
        component: () => import("@/views/floatingBall/viewdialog.vue")
      }
    ]
  },
  {
    path: "/config",
    name: "config",
    component: () => import("@/views/config/index.vue"),
    meta: {
      title: "系统设置"
    }
  },
  {
    path: '/quick-note-editor',
    name: 'QuickNoteEditor',
    component: () => import('@/views/home/<USER>/InlineNoteEditor.vue'),
    meta: {
      title: '闪记编辑器'
    }
  },
  {
    path: '/note-manager',
    name: 'NoteManager',
    component: () => import('@/views/home/<USER>/NoteManager.vue'),
    meta: {
      title: '笔记管理'
    }
  },
  {
    path: '/ai-chat',
    name: 'AiChat',
    component: () => import('@/views/ai-chat/index.vue'),
    meta: {
      title: 'AI智能对话'
      }
  },
  {
    path: "/loading",
    name: "loading",
    component: () => import("@/views/loading/index.vue"),
    meta: {
      title: "loading"
    }
  },
];

/**
 * errorRouter(错误路由)
 */
export const errorRouter = [
  {
    path: "/403",
    name: "403",
    component: () => import("@/components/ErrorMessage/403.vue"),
    meta: {
      title: "403页面"
    }
  },
  {
    path: "/404",
    name: "404",
    component: () => import("@/components/ErrorMessage/404.vue"),
    meta: {
      title: "404页面"
    }
  },
  {
    path: "/500",
    name: "500",
    component: () => import("@/components/ErrorMessage/500.vue"),
    meta: {
      title: "500页面"
    }
  },
  // 当用户请求路径找不到定义的路由时，会跳转到404页面
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/components/ErrorMessage/404.vue")
  }
];
