<template>
  <div class="contact space-y-2 relative" v-loading="loading" element-loading-background="white">
    <div v-if="user?.length == 0">
      <NoData />
    </div>
    <div v-else>
      <RecycleScroller class="scroller" :items="user" :item-size="70" key-field="id" v-slot="{ item }">
        <div class="user-item flex items-center gap-3 rounded-lg p-3 hover:bg-gray-100 cursor-pointer" @click="openChat(item)">
          <div @click.stop>
            <DynamicAvatar
              :id="item.id"
              :relation-name="item.name"
              :data-info="item"
              :type-avatar="0"
              :size="40"
              :online="item.online"
            />
          </div>
          <div class="flex-1 flex flex-col">
            <div class="flex items-center mb-1">
              <h4 class="text-sm font-medium text-gray-900 mr-2">{{ item.name }}</h4>
              <LevelBtn :message="item.secretLevel" data-type="user"></LevelBtn>
            </div>
            <p class="text-xs text-gray-500">{{ item.pathName || item.orgName }}</p>
          </div>
          <div class="actions" @click.stop>
            <el-tooltip content="发送消息" placement="top">
              <el-button link icon="chat-dot-round" @click="openChat(item)"></el-button>
            </el-tooltip>
            <!-- <el-tooltip content="用户详情" placement="top">
              <el-button link icon="more" @click="openProfile(item.id)"></el-button>
            </el-tooltip> -->
          </div>
        </div>
      </RecycleScroller>
    </div>
  </div>
</template>

<script setup lang="ts" name="SingleContact">
import { useTalkStore } from "@/stores/modules/talk";
import { useUserStore } from "@/stores/modules/user";
import { useRecentStore } from "@/stores/modules/recent";
import LevelBtn from "@/components/LevelBtn/index.vue";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import NoData from "@/components/NoData/index.vue";
import { RecycleScroller } from "vue-virtual-scroller";
import "vue-virtual-scroller/dist/vue-virtual-scroller.css";

const talkStore = useTalkStore();
const userStore = useUserStore();
const recentStore = useRecentStore();

defineProps({
  user: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean
  }
});

const emits = defineEmits(["search-click"]);
const openChat = async (data: any) => {
  emits("search-click");
  const params = {
    senderId: userStore.userId,
    contactId: data.id,
    chatType: 0,
    avatar: data.avatar,
    contactName: data.name,
    secret: data.secretLevel
  };
  const res = await recentStore.addListRecents(params);
  if (res) {
    talkStore.setActiveChat(data.id);
    talkStore.ifChat = true;
    talkStore.ifContact = false;
  }
};
</script>

<style scoped lang="scss">
.actions {
  .el-button {
    @apply text-xl;
  }
}

.dark {
  .user-item {
    @apply hover:bg-gray-700;
  }

  h4 {
    @apply text-white;
  }

  p {
    @apply text-gray-400;
  }
}
</style>
