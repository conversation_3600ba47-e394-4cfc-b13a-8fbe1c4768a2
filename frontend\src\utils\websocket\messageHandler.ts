import { MessageInfo, MessageOnlineInfo, MessageSendInfo } from "./messageInfo";
import { useRecentStore } from "@/stores/modules/recent";
import { useHistoryStore } from "@/stores/modules/history";
import { useUserStore } from "@/stores/modules/user";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { ContactsInfo, useContactsStore } from "@/stores/modules/contacts";
import { useNoticeStore } from "@/stores/modules/notices";
import { MessageCode, ContentType } from "./messageInfo";
import { ipc, isEE } from "../ipcRenderer";
import { ipcApiRoute } from "@/ipc/ipcApi";
import WebSocketClient from "./websocketClient";
import * as dbApi from "@/utils/indexedDB";
import dayjs from "dayjs";
import { setReadTime } from "@/api/modules/history";

const recentStore = useRecentStore();
const historyStore = useHistoryStore();
const userStore = useUserStore();
const talkStore = useTalkStore();
const contactStore = useContactStore();
const contactsStore = useContactsStore();
const noticeStore = useNoticeStore();

const saveRecent = async (msg: any) => {
  const params = {
    senderId: userStore.userId,
    contactId: msg.isGroup ? msg.toId.id : msg.fromId.id,
    chatType: msg.chatType,
    avatar: msg.isGroup ? msg.toId.avatar : msg.fromId.avatar,
    contactName: msg.isGroup ? msg.toId.name : msg.fromId.name,
    secret: msg.isGroup ? msg.toId.secretLevel : msg.fromId.secretLevel
  };
  await recentStore.addListRecents(params);

  recentStore.updateLastMsg(
    msg.isGroup ? msg.toId.id : msg.fromId.id,
    msg.content.msg,
    msg.id,
    msg.contentType,
    msg.content.time
  );

  const contactId = msg.isGroup ? msg.toId.id : msg.fromId.id;
  if (!historyStore.msgIds[contactId]?.includes(msg.id)) {
    if (msg.atId?.length > 0) {
      await recentStore.addAtNum(msg.isGroup ? msg.toId.id : msg.fromId.id, msg.atId);
    }
    await recentStore.addUnreadNum(msg.isGroup ? msg.toId.id : msg.fromId.id);

    let message = {
      contact: {
        avatar: msg.fromId.avatar,
        chatType: msg.chatType,
        contactId: msg.isGroup ? msg.toId.id : msg.fromId.id,
        contactName: msg.isGroup ? msg.toId.name : msg.fromId.name,
        secret: msg.isGroup ? msg.toId.secretLevel : msg.fromId.secretLevel
      },
      msgId: msg.id,
      msg: msg.content.msg
    };
    dbApi.addData(message);
  }
};

const saveHistory = (msg: any) => {
  const newMsg = {
    avatar: msg.fromId.avatar,
    cancel: 0,
    createTime: msg.content.time,
    deleteFlag: 1,
    id: msg.id,
    quoteContent: msg.content.quoteContent,
    quoteMessageFile: {
      contentId: msg.content.quoteMessageFile?.contentId,
      msgType: msg.content.quoteMessageFile?.msgType,
      msg: msg.content.quoteMessageFile?.msg
    },
    quoteSenderName: msg.content.quoteSenderName,
    msg: msg.content.msg,
    fielId: msg.content?.fileId,
    chatType: msg.chatType,
    contentId: msg.contentId,
    msgType: msg.contentType,
    receiver: msg.toId.id,
    secret: msg.content.secret,
    sender: msg.fromId.id,
    senderName: msg.fromId.name,
    isTop: "0",
    online: "on",
    isRead: talkStore.activeChatId == msg.fromId.id ? "0" : "1"
  };

  // 存储消息数据到客户端
  if (isEE) {
    let ipcMsg = JSON.stringify(newMsg);
    if (newMsg.chatType) {
      ipc.invoke(ipcApiRoute.addGroupMessage, ipcMsg);
    } else {
      ipc.invoke(ipcApiRoute.addUserMessage, ipcMsg);
    }
  }

  if (msg.isGroup && msg.content?.fileId && msg.fromId.id == userStore.userId) {
    historyStore.updateHistory(msg.toId.id, msg.content?.fileId, newMsg);
  } else {
    historyStore.setMsgHistory(msg.isGroup ? msg.toId.id : msg.fromId.id, msg.id, newMsg);
  }

  if (newMsg.isRead == "0" && newMsg.chatType == 0) {
    const webSocketManager: any = WebSocketClient.getInstance();
    let readMsg: MessageSendInfo = {
      code: MessageCode.PRAIVTE,
      data: {
        id: "",
        fromId: userStore.userId,
        toId: talkStore.activeChatId,
        atId: [],
        isGroup: false,
        contentId: "",
        content: {},
        contentType: ContentType.UNREAD
      }
    };
    webSocketManager.send(readMsg);
    let params = {
      receiver: talkStore.activeChatId,
      sender: userStore.userId,
      readTime: dayjs(msg.content.time).format("YYYY-MM-DD HH:mm:ss")
    };
    setReadTime(params);
  }
};

export class MessageHandler {
  private handlers: Map<number, (message: MessageInfo) => void> = new Map();
  private handlersOnline: Map<number, (message: MessageOnlineInfo) => void> = new Map();
  constructor() {
    this.registerHandlers();
  }

  private registerHandlers(): void {
    this.handlers.set(200200, this.handleGroupMessage.bind(this));
    this.handlers.set(200100, this.handlePrivateMessage.bind(this));
    this.handlers.set(200300, this.handleSystemMessage.bind(this));
    this.handlers.set(200203, this.handleSubscriptionMessage.bind(this));
    this.handlers.set(200201, this.handleGroupRelay.bind(this));
    this.handlers.set(200202, this.handleGroupVote.bind(this));
    this.handlers.set(200204, this.handleGroupChange.bind(this));
    this.handlers.set(200205, this.handleOfficialDoc.bind(this));
    this.handlersOnline.set(700100, this.handleOnlineMessage.bind(this));
    this.handlersOnline.set(700200, this.handleOfflineMessage.bind(this));
    this.handlersOnline.set(MessageCode.GET_ONLINE_STATUS, this.handleUpdateOnlineStatus.bind(this));
  }

  public handleMessage(message: any): void {
    const handler = this.handlers.get(message.code);
    const handlerOnline = this.handlersOnline.get(message.code);
    if (handlerOnline) {
      handlerOnline(message);
    }
    if (handler) {
      handler(message);
    }
  }

  private async handleGroupMessage(message: MessageInfo): Promise<void> {
    message.data.forEach(async item => {
      if (item.contentType == ContentType.BACKSPACE) {
        // 处理撤回消息
        historyStore.deleteMsgHistory(item.toId.id, item.content.id, item.content.name);
        recentStore.updateCancel(item.toId.id, item.content.name);
      } else {
        this.trayNotificationHandle(item, message.code);
        // 投票，接龙，公文
        if (item.contentType && [4, 5, 9].includes(item.contentType)) {
          if (talkStore.activeChatId === item.toId.id) {
            historyStore.getGroupActivity(item.toId.id);
          }
        }
        // 处理群组消息的逻辑
        await saveRecent(item);
        saveHistory(item);
      }
    });
  }

  private async handlePrivateMessage(message: MessageInfo): Promise<void> {
    message.data.forEach(async item => {
      if (item.contentType == ContentType.BACKSPACE) {
        // 处理撤回消息
        historyStore.deleteMsgHistory(item.fromId.id, item.content.id, item.content.name);
        recentStore.updateCancel(item.fromId.id, item.content.name);
      } else if (item.contentType == ContentType.UNREAD) {
        // 处理消息状态已读
        historyStore.updateMsgRead(item.fromId.id);
      } else {
        this.trayNotificationHandle(item, message.code);
        // 处理私人消息的逻辑
        await saveRecent(item);
        saveHistory(item);
      }
    });
  }

  private handleSystemMessage(message: MessageInfo): void {
    message.data.forEach(item => {
      this.trayNotificationHandle(item, message.code);
      // 处理系统消息的逻辑
      noticeStore.addNotices(item.content);
    });
  }

  private handleSubscriptionMessage(message: MessageInfo): void {
    message.data.forEach(async item => {
      // 处理订阅消息的逻辑
    });
  }

  private handleOnlineMessage(message: MessageOnlineInfo): void {
    message.data?.forEach(item => {
      let contactInfo = contactsStore.getCacheContactById(item.userId)
      if (contactInfo) {
        contactInfo.isOnline = true
        contactsStore.updateContact(contactInfo, ["isOnline"]);
        contactsStore.updateSqliteData();
      }
    });
  }

  private handleOfflineMessage(message: MessageOnlineInfo): void {
    message.data?.forEach(item => {
      let contactInfo = contactsStore.getCacheContactById(item.userId)
      if (contactInfo) {
        contactInfo.isOnline = false
        contactsStore.updateContact(contactInfo, ["isOnline"]);
        contactsStore.updateSqliteData();
      }
    })
  }

  private async handleUpdateOnlineStatus(message: any) {
    if (message?.data) {
      // 先重置状态为离线
      contactsStore.ContactMap.forEach(item => {
        item.isOnline = false
      })
      contactsStore.updateSqliteData()

      let { users = [], groups = [] } = message.data;
      for (let item of users) {
        const { status, userId, userName, avatar, secretLevel } = item;
        if (status === null) break;
        
        contactsStore.updateContact(
          {
            id: userId,
            name: userName,
            avatar,
            secretLevel,
            isOnline: status == "on"
          },
          ["isOnline"]
        );
      }
      
      contactsStore.updateSqliteData();

      let list = await recentStore.getListRecents();
      for (let item of list) {
        if (item.chatType > 0) {
          if (!groups.some((v: { groupId: any }) => v.groupId == item.contactId)) {
            recentStore.deleteListRecents(item.id);
          }
        }
      }
    }
  }

  /**
   * 群组接龙消息处理
   * @param message
   */
  private handleGroupRelay(message: MessageInfo): void {
    message.data.forEach(item => {
      const { relayId = "", eventType = "", id = "" } = item.content;
      if (eventType == "delete") {
        historyStore.deleteParticipant(relayId, id);
      } else {
        historyStore.setRelayRecords(relayId, item.content);
      }
    });
  }

  /**
   * 群组投票消息处理
   * @param message
   */
  private handleGroupVote(message: MessageInfo): void {
    message.data.forEach(item => {
      const { voteId = "", optionId = "", resData } = item.content;
      historyStore.setVoteRecords(voteId, optionId, resData);
    });
  }

  /**
   * 群组信息变更处理
   * @param message
   */
  private handleGroupChange(message: MessageInfo): void {
    message.data.forEach(item => {
      const { contentType, content } = item;
      if (contentType == ContentType.GROUP_MODIFY_NAME) {
        // 修改群组名称
        recentStore.updateGroupName(content.id, content.groupName);
        if (content.id == talkStore.activeChatId) talkStore.updateActiveChat(content.groupName);
        contactStore.updateGroupInfo(content.id, content.groupName, null);
      } else if (contentType == ContentType.GROUP_MODIFY_NOTICE) {
        // 修改群组公告
        contactStore.updateGroupInfo(content.id, null, content.groupNotice);
      } else if (contentType == ContentType.GROUP_MODIFY_OWNER) {
        // 修改群组群主
        contactStore.updateGroupInfo(content.groupId, null, null, content.groupOwnerId, content.groupOwnerName);
      } else if (contentType == ContentType.GROUP_ADD_MEMBER) {
        // 群组加人
        contactStore.getGroupMember(content.groupId);
      } else if (contentType == ContentType.GROUP_DELETE_MEMBER) {
        // 群组减人
        contactStore.deleteGroupMember(content.groupId, content.ids, content.userList);
      } else if (contentType == ContentType.GROUP_DISSLOVE) {
        // 群组解散
        contactStore.leaveGroup(content.id);
      } else if (contentType == ContentType.GROUP_LEAVE) {
        // 退出群组
        if (userStore.userId == content.id) {
          contactStore.leaveGroup(content.groupId);
        } else {
          contactStore.deleteGroupMember(content.groupId, content.id);
        }
      } else if (contentType == ContentType.PIN) {
        //置顶消息
        historyStore.pinMsgHistory(item.toId.id, item.content.id);
      } else {
        //取消置顶
        historyStore.pinMsgHistory(item.toId.id);
      }
    });
  }

  private trayNotificationHandle(data: any, code: any) {
    // 当前私聊对话消息，不提醒
    if (code === MessageCode.PRAIVTE && talkStore.activeChatId === data.fromId.id) return;
    // 自己发送的群组消息或当前群组对话消息，不提醒
    if (code === MessageCode.GROUP && (userStore.userId === data.fromId.id || talkStore.activeChatId === data.toId.id)) return;

    let title = data.content?.title;
    let content = data.content?.msg;
    let contentType = data.contentType;
    const type = code;
    switch (code) {
      case MessageCode.GROUP:
        title = data.toId.name;
        content = data.content?.msg;
        break;
      case MessageCode.SYSTEM:
        title = data.content.title;
        content = data.content?.content;
        break;
      case MessageCode.PRAIVTE:
        title = data.fromId.name;
        content = data.content?.msg;
        break;
      default:
        return;
    }
    // 处理通知的逻辑
    ipc?.invoke(ipcApiRoute.trayNotification, {
      title: title,
      content: content,
      type: type,
      contentType: contentType
    });
    ipc?.send("message-start-blinking");
  }

  private handleOfficialDoc(message: MessageInfo) {
    message.data.forEach(item => {
      const { docId = "" } = item.content;
      historyStore.setParticipateOfficialDoc(docId, item.content);
    });
  }
}
