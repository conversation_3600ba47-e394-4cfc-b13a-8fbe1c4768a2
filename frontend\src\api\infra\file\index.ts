import request from "@/api";
import { AxiosRequestConfig } from "axios";

export interface FilePageReqVO extends PageParam {
  path?: string;
  type?: string;
  createTime?: Date[];
}

// 文件预签名地址 Response VO
export interface FilePresignedUrlRespVO {
  // 文件配置编号
  configId: number;
  // 文件上传 URL
  uploadUrl: string;
  // 文件 URL
  url: string;
}

// 查询文件列表
export const getFilePage = (params: any) => {
  return request.get("/admin-api/infra/file/page", params);
};

// 删除文件
export const deleteFile = (id: number) => {
  return request.delete("/admin-api/infra/file/delete?id=" + id);
};

// 获取文件预签名地址
export const getFilePresignedUrl = (path: string) => {
  return request.get<FilePresignedUrlRespVO>("/admin-api/infra/file/presigned-url", { params: { path } });
};

// 创建文件
export const createFile = (data: any) => {
  return request.post("/admin-api/infra/file/create", data);
};

// 上传文件
export const updateFile = (data: any, config: AxiosRequestConfig<any> | undefined) => {
  // return request.post("/infra/file/upload", data, {
  const defaultConfig = {
    headers: { "Content-Type": "multipart/form-data" }
  };

  if (config) {
    Object.assign(defaultConfig, config);
  }

  return request.post("/admin-api/infra/file/chunkedUpload", data, defaultConfig);
};

// 获取文件
export const getFileUrl = (params: any) => {
  return request.get("/admin-api/infra/file/download", { params });
};

// 下载文件流
export const getFileUrls = (params: any) => {
  return request.get("/admin-api/infra/file/downloadByUrl?id="+ params);
};

// 下载文件url
export const getFileUrlFun = (params: any) => {
  return request.get("/admin-api/infra/file/image-direct", { params });
};