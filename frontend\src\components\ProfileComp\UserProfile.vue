<template>
  <el-dialog v-model="visible" :show-close="false" class="custom-dialog profile">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'calendar']" class="icon" />
        <span class="title">个人信息</span>
      </div>
    </template>
    <div class="text-center">
      <div class="relative">
        <el-upload :show-file-list="false" :http-request="handleChange" accept="image/*">
          <DynamicAvatar
            :id="userStore.userId"
            :data-info="userStore.userInfo"
            :relation-name="userStore.name"
            :type-avatar="0"
            :size="64"
          />
          <div class="mask">
            <el-button link icon="EditPen" style="color: #eee; font-size: 20px"></el-button>
          </div>
        </el-upload>
      </div>
      <div class="username pb-4 text-2xl font-medium text-gray-900 border-b border-gray-200">{{ userStore.userInfo.name }}</div>
    </div>
    <vue-cropper
      v-if="imageUrl"
      ref="cropper"
      :src="imageUrl"
      alt="Source Image"
      :aspect-ratio="1 / 1"
      style="max-height: 200px; overflow: hidden"
    ></vue-cropper>
    <div v-if="imageUrl" class="mt-4">
      <el-button type="primary" :loading="uploading" @click="cropImage">裁剪并上传</el-button>
    </div>
    <div class="pt-6 space-y-6 text-base">
      <el-row>
        <el-col :span="4" class="text-gray-500 mt-1">组织</el-col>
        <el-col :span="20">
          <el-input v-model="userStore.userInfo.orgName" readonly />
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="text-gray-500 mt-1">电话</el-col>
        <el-col :span="20">
          <el-input v-model="otel" readonly v-if="editOtel">
            <template #suffix>
              <el-button link icon="edit" @click="editOtel = false"></el-button>
            </template>
          </el-input>
          <el-input v-model="otel" @input="v => (otel = v.replace(/[^\d]/g, ''))" maxlength="11" v-else>
            <template #suffix>
              <el-button link icon="check" @click="save(1)"></el-button>
            </template>
          </el-input>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="4" class="text-gray-500 mt-1">个性签名</el-col>
        <el-col :span="20">
          <el-input v-model="description" readonly v-if="editDesc">
            <template #suffix>
              <el-button link icon="edit" @click="editDesc = false"></el-button>
            </template>
          </el-input>
          <el-input v-model="description" v-else>
            <template #suffix>
              <el-button link icon="check" @click="save(3)"></el-button>
            </template>
          </el-input>
        </el-col>
      </el-row>
    </div>
    <div class="over-mask" v-if="uploading">
      <el-icon :size="100">
        <loading class="is-loading" />
      </el-icon>
    </div>
  </el-dialog>
</template>

<script setup lang="ts" name="user">
import { ref } from "vue";
import { uploadAvatar } from "@/api/modules/login";
import { useUserStore } from "@/stores/modules/user";
import DynamicAvatar from "@/components/Avatar/DynamicAvatar.vue";
import { ElMessage } from "element-plus";
import VueCropper from "vue-cropperjs";
import "cropperjs/dist/cropper.css";
import { ContactsInfo, useContactsStore } from "@/stores/modules/contacts";

const contactsStore = useContactsStore();
const userStore = useUserStore();
const cropper = ref<any>(null);
const imageUrl = ref<any>("");
const uploading = ref(false);
const handleChange: any = (file: any) => {
  const reader = new FileReader();
  imageUrl.value = "";
  reader.onload = (e: any) => {
    imageUrl.value = e.target.result;
  };
  reader.readAsDataURL(file.file);
};

const cropImage = async () => {
  uploading.value = true;
  try {
    cropper.value.getCroppedCanvas().toBlob(async (blob: any) => {
      const formData = new FormData();
      formData.append("avatarFile", blob);
      let res = await uploadAvatar(formData);
      userStore.userInfo.avatar = res.data;
      imageUrl.value = "";
      ElMessage.success("修改成功");
      // 更新头像
      contactsStore.updateContact(userStore.userInfo as ContactsInfo, ["avatar"]);
      
      contactsStore.updateSqliteData();
    });
  } finally {
    uploading.value = false;
  }
};

const visible = ref(false);
const openDialog = () => {
  visible.value = true;
};
const closeDialog = () => {
  visible.value = false;
};
defineExpose({ openDialog, closeDialog });

const otel = ref(userStore.userInfo.otel);
const oemail = ref(userStore.userInfo.oemail);
const description = ref(userStore.userInfo.description);

const editOtel = ref(true);
const editOemail = ref(true);
const editDesc = ref(true);

const save = async (num: number) => {
  let params: any = {};
  if (num == 1) {
    // 11位时校验真实手机号，否则通过
    if (otel.value.length == 11 && !new RegExp(/^1[3-9]\d{9}$/).test(otel.value)) {
      ElMessage.error("电话号码错误");
      return;
    }
    params = {
      id: userStore.userId,
      otel: otel.value
    };
  } else if (num == 2) {
    if (!new RegExp(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/).test(oemail.value)) {
      ElMessage.error("邮箱错误");
      return;
    }
    params = {
      id: userStore.userId,
      oemail: oemail.value
    };
  } else {
    if (description.value?.length > 20) {
      ElMessage.error("个性签名长度不能超过20");
      return;
    }
    params = {
      id: userStore.userId,
      description: description.value
    };
  }
  await userStore.updateUserInfoFc(params, num);
  ElMessage.success("操作成功");
  if (num == 1) editOtel.value = true;
  if (num == 2) editOemail.value = true;
  if (num == 3) editDesc.value = true;
};
</script>

<style scoped lang="scss">
.profile {
  @apply relative;
  :deep(.el-input--default) {
    @apply flex-1;

    .el-input__suffix {
      @apply cursor-pointer;
    }
  }

  .mask {
    width: 64px;
    height: 64px;
    position: absolute;
    left: 50%;
    top: 0;
    z-index: 10;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.2);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .over-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.is-loading {
  color: #fff;
  animation: loading-rotate 3s linear infinite;
}

@keyframes loading-rotate {
  to {
    transform: rotate(360deg);
  }
}

.dark {
  .username {
    @apply text-white border-gray-700;
  }
}
</style>
