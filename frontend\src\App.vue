<script setup lang="ts">
import { computed, reactive, onMounted, watch } from "vue";
import { useGlobalStore } from "./stores/modules/global";
import { useIpcEvent } from "./hooks/useIpcEvent";
import { ipc } from "@/utils/ipcRenderer";

const globalStore = useGlobalStore();

const assemblySize = computed(() => globalStore.assemblySize);
const buttonConfig = reactive({ autoInsertSpace: false });

// 监听窗口最大化状态
const isMaximized = computed(() => globalStore.maximize);

// 监听最大化状态变化，动态切换body class
watch(
  isMaximized,
  newValue => {
    if (newValue) {
      document.body.classList.add("window-maximized");
    } else {
      document.body.classList.remove("window-maximized");
    }
  },
  { immediate: true }
);

onMounted(() => {
  useIpcEvent();

  // 监听主窗口最大化状态变化
  if (ipc) {
    ipc.on("window-maximize-state-changed", (_event: any, newState: boolean) => {
      globalStore.setGlobalState("maximize", newState);
    });

    // 初始化时获取窗口状态
    ipc
      .invoke("controller.os.getMaximizeState")
      .then((state: boolean) => {
        globalStore.setGlobalState("maximize", state);
      })
      .catch(() => {
        // 如果获取失败，使用默认值
        globalStore.setGlobalState("maximize", false);
      });
  }
});
</script>

<template>
  <el-config-provider :size="assemblySize" :button="buttonConfig">
    <router-view></router-view>
  </el-config-provider>
</template>

<style>
img,
a {
  -webkit-user-drag: none;
  -o-user-drag: none;
  -moz-user-drag: none;
}
/* 确保没有默认边距和填充 */
html,
body {
  margin: 0;
  padding: 0;
  overflow: hidden;
}

body #app {
  margin: 0;
  padding: 0;
  width: 100vw;
  height: 100vh;
  background: transparent;
  box-sizing: border-box;
}

/* 暗色主题的 app 背景 */
.dark body #app {
  background: transparent;
}

body .app-container {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  background: #ffffff;
  border: 2px solid #d1d5db; /* 直角边框 */
  border-radius: 0; /* 确保直角 */
  box-sizing: border-box;
  /* 添加阴影效果 */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 暗色主题 */
.dark body .app-container {
  background: #1f2937;
  border-color: #374151; /* 暗色主题边框 */
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 2px 8px rgba(0, 0, 0, 0.2);
}

/* 窗口最大化状态样式 */
body.window-maximized .app-container {
  border: none !important;
  box-shadow: none !important;
}

/* 使用原生边框后，不再需要 Windows 7 兼容性样式 */

.app-container .el-overlay,
.el-overlay {
  border-radius: 0;
}

/* Element Plus 弹窗样式 */
.el-popover.el-popper {
  min-width: 20px !important;
}

/* 修改整个应用的滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #999;
}

/* 暗色主题滚动条 */
.dark ::-webkit-scrollbar-track {
  background: #2a2a2a;
}

.dark ::-webkit-scrollbar-thumb {
  background: #555;
}

/* 使用原生边框后，样式大大简化 */
</style>
