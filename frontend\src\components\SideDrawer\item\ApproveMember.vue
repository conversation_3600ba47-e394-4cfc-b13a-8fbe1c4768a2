<template>
  <el-dialog v-model="visible" :show-close="false" :close-on-click-modal="false" class="custom-dialog need-foot">
    <template #header>
      <button class="close-btn" @click="closeDialog">
        <font-awesome-icon :icon="['fas', 'times']" />
      </button>
      <div class="flex items-center gap-2">
        <font-awesome-icon :icon="['fas', 'users']" class="icon" />
        <span class="title">审批</span>
      </div>
    </template>
    <template #default>
      <div class="flex justify-between member">
        <el-tabs v-model="activeTab" class="custom-tabs">
          <el-tab-pane label="组织架构" name="1">
            <el-scrollbar height="240">
              <org-tree :group-scope="groupInfo.groupScope" @node-click="handleClick"></org-tree>
            </el-scrollbar>
          </el-tab-pane>
        </el-tabs>
        <el-transfer
          class="custom-transfer"
          v-model="transferValue"
          filterable
          filter-placeholder="搜索成员"
          :titles="['可选择人员', '已选择人员']"
          :button-texts="['移除', '添加']"
          :props="transferProps"
          :data="transferData"
        >
          <template #default="{ option }">
            <span>{{ option.name }}</span>
          </template>
        </el-transfer>
      </div>
    </template>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts" name="Approval">
import { computed, ref } from "vue";
import { getOrgUser } from "@/api/modules/org";
import OrgTree from "@/components/tree/org-tree.vue";
import { useTalkStore } from "@/stores/modules/talk";
import { useContactStore } from "@/stores/modules/friend";
import { useContactsStore } from "@/stores/modules/contacts";

const talkStore = useTalkStore();
const contactStore = useContactStore();
const contactsStore = useContactsStore()
const visible = ref(false);
const closeDialog = () => {
  visible.value = false;
};
const openDialog = () => {
  visible.value = true;
  transferValue.value = [];
};
defineExpose({ openDialog, closeDialog });

const groupInfo = computed(() => contactStore.groupInfo[talkStore.activeChatId] || {});

const activeTab = ref("1");
const transferData = ref<any>([]);
const transferValue = ref<any>([]);
const transferProps = {
  key: "id",
  label: "name"
};
const handleClick = async (orgCode: string) => {
  let newList: any[] = [];
  const params = {
    orgCode: orgCode,
    roleCode: groupInfo.value.groupScope
  };
  const res: any = await getOrgUser(params);
  if (res.code == 0) {
    newList = res.data;

    newList?.forEach((item: any) => {
      let fileds: any = ["avatar", 'isOnline']
      if (item.online === null) fileds = ["avatar"]
      // 更新用户状态
      contactsStore.updateContact(
        {
          id: item.id,
          name: item.name,
          avatar: item.avatar,
          secretLevel: item.secretLevel,
          isOnline: item.online == "on"
        },
        fileds
      );
    })
    
    contactsStore.updateSqliteData();
    // 找出 transferValue 中的选中项，不在 newList 中的（来自旧数据）
    const selectedSet = new Set(transferValue.value);
    const preservedItems = transferData.value.filter(
      (item: any) => selectedSet.has(item.id) && !newList.some(n => n.id === item.id)
    );
    // 合并新数据和已选中的旧数据
    transferData.value = [...newList, ...preservedItems];
  }
};

const emit = defineEmits(["sure"]);

const handleConfirm = () => {
  const arr = transferData.value.filter((item: any) => transferValue.value.includes(item.id));
  const approve = arr.map((item: any) => {
    return {
      userId: item.id,
      userName: item.name
    };
  });
  emit("sure", approve);
};
</script>

<style lang="scss" scoped></style>
